{"permissions": {"allow": ["Bash(cd /Users/<USER>/code/faith-magic-bg/packages/i18n)", "Bash(node:*)", "Bash(cp:*)", "Bash(ls -la \"/Users/<USER>/code/faith-magic-bg/packages/i18n/translations/\")", "Bash(cd \"/Users/<USER>/code/faith-magic-bg/packages/i18n\")", "Bash(node -e \"\nconst fs = require(''fs'');\n\nfunction getAllKeys(obj, prefix = '''') {\n  const keys = new Set();\n  function traverse(current, path) {\n    if (current && typeof current === ''object'' && !Array.isArray(current)) {\n      Object.keys(current).forEach(key => {\n        const fullPath = path ? \\`${path}.${key}\\` : key;\n        traverse(current[key], fullPath);\n      });\n    } else {\n      keys.add(path);\n    }\n  }\n  traverse(obj, prefix);\n  return keys;\n}\n\nconst enContent = JSON.parse(fs.readFileSync(''translations/en.json'', ''utf8''));\nconst enKeys = getAllKeys(enContent);\n\nconsole.log(\\`English file has ${enKeys.size} total keys\\`);\n\nconst languages = [''de'', ''es'', ''fr'', ''ja'', ''ko'', ''pt'', ''ru'', ''th'', ''vi'', ''zh-CN'', ''zh-HK'', ''zh-TW''];\n\nlanguages.forEach(lang => {\n  const langContent = JSON.parse(fs.readFileSync(\\`translations/${lang}.json\\`, ''utf8''));\n  const langKeys = getAllKeys(langContent);\n  const missingKeys = [...enKeys].filter(key => !langKeys.has(key));\n  const extraKeys = [...langKeys].filter(key => !enKeys.has(key));\n  \n  console.log(\\`${lang}.json: ${langKeys.size} keys, missing: ${missingKeys.length}, extra: ${extraKeys.length}\\`);\n  \n  if (missingKeys.length > 0) {\n    console.log(\\`  Missing keys: ${missingKeys.slice(0, 5).join('', '')}${missingKeys.length > 5 ? ''...'' : ''''}\\`);\n  }\n  if (extraKeys.length > 0) {\n    console.log(\\`  Extra keys: ${extraKeys.slice(0, 5).join('', '')}${extraKeys.length > 5 ? ''...'' : ''''}\\`);\n  }\n});\n\")", "Bash(rm align-translations.js verify-alignment.js)"], "deny": []}}