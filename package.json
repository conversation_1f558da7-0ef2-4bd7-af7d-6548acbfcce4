{"name": "supastarter-nextjs", "private": true, "scripts": {"build": "dotenv -c -- turbo build", "dev": "dotenv -c -- turbo dev --concurrency 15", "lint": "biome lint .", "clean": "turbo clean", "format": "biome format . --write", "db:push": "turbo db:push", "db:generate": "turbo db:generate", "db:studio": "pnpm --filter database run db:studio", "mail:preview": "pnpm --filter mail run preview", "e2e": "pnpm --filter web e2e"}, "engines": {"node": ">=20"}, "packageManager": "pnpm@9.3.0", "devDependencies": {"@biomejs/biome": "1.9.4", "dotenv-cli": "^7.4.2", "tsconfig": "workspace:*", "turbo": "^2.3.0", "typescript": "5.6.3"}, "pnpm": {"overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}}