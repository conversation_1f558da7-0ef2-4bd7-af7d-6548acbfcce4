# supastarter for Next.js

supastarter is the ultimate starter kit for production-ready, scalable SaaS applications.

## Helpful links

- [📘 Documentation](https://supastarter.dev/docs/nextjs)
- [🚀 Demo](https://demo.supastarter.dev)
- [💬 Discord](https://discord.gg/BZDNtf8hqt)

表名: image_generation_history
| 字段名 | 数据类型 | 说明 | 约束 |
|---|---|---|---|
| id | uuid | 记录的唯一标识符 | PRIMARY KEY, DEFAULT uuid_generate_v4() |
| user_id | text | 用户 ID/邮箱 | NOT NULL, 外键关联用户表 |
| original_image_url | text | 原始上传图片的 URL | NOT NULL |
| generated_image_url | text | 生成图片的 URL | NOT NULL |
| prompt | text | 用户输入的提示文本 | NOT NULL |
| ratio | text | 图片生成比例 | NOT NULL |
| created_at | timestamptz | 记录创建时间 | NOT NULL, DEFAULT now() |
| updated_at | timestamptz | 记录更新时间 | DEFAULT now() |
| is_deleted | boolean | 标记是否已删除 | DEFAULT false |
| extra_data | text | 额外信息 | DEFAULT NULL |
