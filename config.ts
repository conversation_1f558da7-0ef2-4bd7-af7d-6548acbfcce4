export const config = {
  i18n: {
    locales: {
      en: {
        currency: 'USD',
        label: 'English',
        flag: '🇺🇸',
      },
      es: {
        currency: 'USD',
        label: 'Español',
        flag: '🇪🇸',
      },
      de: {
        currency: 'USD',
        label: 'Deutsch',
        flag: '🇩🇪',
      },
      fr: {
        currency: 'USD',
        label: 'Français',
        flag: '🇫🇷',
      },
      ja: {
        currency: 'JPY',
        label: '日本語',
        flag: '🇯🇵',
      },
      ko: {
        currency: 'KRW',
        label: '한국어',
        flag: '🇰🇷',
      },
      pt: {
        currency: 'BRL',
        label: 'Português',
        flag: '🇵🇹',
      },
      ru: {
        currency: 'RUB',
        label: 'Русский',
        flag: '🇷🇺',
      },
      th: {
        currency: 'THB',
        label: 'ไทย',
        flag: '🇹🇭',
      },
      vi: {
        currency: 'VND',
        label: 'Tiếng Việt',
        flag: '🇻🇳',
      },
      'zh-CN': {
        currency: 'CNY',
        label: '简体中文',
        flag: '🇨🇳',
      },
      'zh-HK': {
        currency: 'HKD',
        label: '繁體中文-香港',
        flag: '🇭🇰',
      },
      'zh-TW': {
        currency: 'TWD',
        label: '繁體中文-台灣',
        flag: '',
      },
    },
    defaultLocale: 'en',
    defaultCurrency: 'USD',
    localeCookieName: 'NEXT_LOCALE',
  },
  teams: {
    avatarColors: ['#4e6df5', '#e5a158', '#9dbee5', '#ced3d9'],
  },
  auth: {
    redirectAfterLogout: '/',
    sessionCookieName: 'auth_session',
    sessionCookieMaxAge: 60 * 60 * 24 * 30,
  },
  mailing: {
    provider: 'plunk',
    from: '<EMAIL>',
  },
  ui: {
    // enabledThemes: ['light', 'dark'],
    enabledThemes: ['light', 'dark'],
    defaultTheme: 'light',
  },
} as const satisfies Config

export type Config = {
  i18n: {
    locales: {
      [locale: string]: {
        currency: string
        label: string
        flag: Element | string
      }
    }
    defaultLocale: string
    defaultCurrency: string
    localeCookieName: string
  }
  teams: { avatarColors: string[] }
  auth: {
    redirectAfterLogout: string
    sessionCookieName: string
    sessionCookieMaxAge: number
  }
  mailing: {
    provider:
      | 'custom'
      | 'console'
      | 'plunk'
      | 'resend'
      | 'postmark'
      | 'nodemailer'
    from: string
  }
  ui: {
    enabledThemes: Array<'light' | 'dark'>
    defaultTheme: Config['ui']['enabledThemes'][number]
  }
}

export type Locale = keyof (typeof config)['i18n']['locales']
