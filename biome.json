{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "formatter": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "complexity": {"noForEach": "off"}, "correctness": {"useExhaustiveDependencies": "off", "noUnusedImports": "error"}, "nursery": {"useSortedClasses": "error", "noRestrictedImports": {"level": "error", "options": {"paths": {"next/link": "Please import from `@i18n` instead."}}}}}}, "organizeImports": {"enabled": true}, "files": {"ignore": [".react-email", ".next", ".turbo", "node_modules/*", ".content-collections", "./packages/database/src/zod/index.ts"]}}