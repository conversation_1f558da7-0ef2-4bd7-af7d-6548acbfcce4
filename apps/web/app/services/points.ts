import { supabase } from '../lib/supabaseClient'

export interface User {
  id: string | number
  email: string
}

export interface PointsServiceResult {
  success: boolean
  code: number
  message: {
    en: string
  }
  data?: {
    points?: number
    newPoints?: number
  }
}

export interface PointsHistory {
  user_id: string | number
  points: number
  type: 'consume' | 'add'
  description: string
  before_points: number
  after_points: number
  create_time: string
}

/**
 * 积分服务
 * 处理所有与用户积分相关的操作，包括验证、消耗和记录
 */
export class PointsService {
  /**
   * 验证用户积分是否足够
   * @param user - 用户信息
   * @param points - 需要消耗的积分数量
   * @param description - 操作描述
   */
  static async validateAndConsumePoints(
    user: User,
    points: number,
    description: string
  ): Promise<PointsServiceResult> {
    try {
      if (!user?.id || !user?.email) {
        return {
          success: false,
          code: 401000,
          message: {
            en: 'Please login first.',
          },
        }
      }

      // 查询用户信息和积分
      const { data: userInfo, error: userError } = await supabase
        .from('removeai_user')
        .select('id, email, points')
        .eq('id', user.id)
        .eq('email', user.email)
        .single()

      if (userError || !userInfo) {
        return {
          success: false,
          code: 400000,
          message: {
            en: 'User not found.',
          },
        }
      }

      // 检查积分是否足够
      if (userInfo.points < points) {
        return {
          success: false,
          code: 400000,
          message: {
            en: 'Insufficient points.',
          },
        }
      }

      return {
        success: true,
        code: 100000,
        message: {
          en: 'Points validation successful.',
        },
        data: {
          points: userInfo.points,
        },
      }
    } catch (error) {
      console.error('Points validation error:', error)
      return {
        success: false,
        code: 500000,
        message: {
          en: 'Failed to validate points.',
        },
      }
    }
  }

  /**
   * 消耗用户积分
   * @param user - 用户信息
   * @param points - 需要消耗的积分数量
   * @param description - 操作描述
   * @param currentPoints - 当前积分数量
   */
  static async consumePoints(
    user: User,
    points: number,
    description: string,
    currentPoints: number
  ): Promise<PointsServiceResult> {
    try {
      const newPoints = currentPoints - points
      console.log('消耗用户积分', currentPoints, points, newPoints, user)
      const { error: updateError } = await supabase
        .from('removeai_user')
        .update({ points: newPoints })
        .eq('id', user.id)
        .eq('email', user.email)

      if (updateError) {
        console.error('Failed to update user points:', updateError)
        return {
          success: false,
          code: 500000,
          message: {
            en: 'Failed to consume points.',
          },
        }
      }

      // 记录积分消耗历史
      const history: PointsHistory = {
        user_id: user.id,
        points: points,
        type: 'consume',
        description,
        before_points: currentPoints,
        after_points: newPoints,
        create_time: new Date().toISOString(),
      }

      const { error: historyError } = await supabase
        .from('removeai_points_history')
        .insert(history)

      if (historyError) {
        console.error('Failed to record points history:', historyError)
        // 记录历史失败不影响主流程，但需要记录日志以便后续处理
      }

      return {
        success: true,
        code: 100000,
        message: {
          en: 'Points consumed successfully.',
        },
        data: {
          newPoints,
        },
      }
    } catch (error) {
      console.error('Points consumption error:', error)
      return {
        success: false,
        code: 500000,
        message: {
          en: 'Failed to consume points.',
        },
      }
    }
  }
}
