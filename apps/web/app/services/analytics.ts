export const analyticsService = {
  trackUserVisit: async (userId: string) => {
    try {
      const response = await fetch('/api/user/track-visit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      })

      if (!response.ok) {
        throw new Error('Failed to track visit')
      }

      return await response.json()
    } catch (error) {
      console.error('Analytics tracking error:', error)
      return null
    }
  },
}
