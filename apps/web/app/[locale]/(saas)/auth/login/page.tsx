// page.tsx
import { getTranslations } from 'next-intl/server'
import { LoginClient } from './LoginPage'
import { UserProfileClient } from '../components/UserProfile'
import { getUserFromCookies } from '../../../../utils/cookies'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('loginPageTitle'),
    description: t('loginPageDesc'),
    keywords: t('loginPageKeywords'),
  }
}

export default async function LoginPage() {
  const user = await getUserFromCookies()

  if (!user) {
    return <LoginClient />
  }

  return <UserProfileClient user={user} />
}
