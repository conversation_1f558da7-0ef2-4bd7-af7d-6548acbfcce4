import { Link } from '@i18n/routing'
import { NavBar } from '@marketing/shared/components/NavBar'
import { UserContextProvider } from '@saas/auth/lib/user-context'
import { Footer } from '@saas/shared/components/Footer'
import { ColorModeToggle } from '@shared/components/ColorModeToggle'
import { LocaleSwitch } from '@shared/components/LocaleSwitch'
import { Logo } from '@shared/components/Logo'
import type { PropsWithChildren } from 'react'

export const dynamic = 'force-dynamic'
export const revalidate = 0

export default function AuthLayout({ children }: PropsWithChildren) {
  return (
    <UserContextProvider initialUser={null}>
      <div className="min-h-screen w-full bg-gradient-to-r from-[#1a0033] via-[#0a001a] to-[#1a0033]">
        <div className="flex min-h-screen w-full flex-col">
          <div className="container h-24 mx-auto px-4 py-4">
            <NavBar />
          </div>

          <main className="flex-1 w-full">{children}</main>

          <Footer />
        </div>
      </div>
    </UserContextProvider>
  )
}
