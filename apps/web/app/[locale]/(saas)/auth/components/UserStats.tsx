// components/UserProfile/UserStats.tsx
import { Coins, Award, Clock, RefreshCw } from 'lucide-react'

interface UserStatsProps {
  points: number
  balance: number
  membershipEndDate?: string | null
  onRenew?: () => void
}

export function UserStats({
  points,
  balance,
  membershipEndDate,
  onRenew,
}: UserStatsProps) {
  const isExpired = membershipEndDate
    ? new Date(membershipEndDate) < new Date()
    : true

  const formatDate = (dateStr?: string | null) => {
    if (!dateStr) return 'Not Available'
    const date = new Date(dateStr)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <div className="space-y-4 mt-6">
      {/* First Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="p-6 rounded-lg bg-white shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <Award className="flex-shrink-0 w-8 h-8 text-purple-600" />
            <div>
              <p className="text-sm text-gray-500">Points</p>
              <p className="text-2xl font-bold text-gray-800">{points}</p>
            </div>
          </div>
        </div>

        <div className="p-6 rounded-lg bg-white shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <Coins className="flex-shrink-0 w-8 h-8 text-purple-600" />
            <div>
              <p className="text-sm text-gray-500">Balance</p>
              <p className="text-2xl font-bold text-gray-800">${balance}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Second Row */}
      <div className="w-full">
        <div className="p-6 rounded-lg bg-white shadow-sm border border-gray-200">
          <div className="flex items-center space-x-4">
            <Clock className="flex-shrink-0 w-8 h-8 text-purple-600" />
            <div className="flex-1 min-w-0">
              <p className="text-sm text-gray-500">Subscription Expires</p>
              <p className="text-2xl font-bold text-gray-800">
                {formatDate(membershipEndDate)}
              </p>
              {isExpired && onRenew && (
                <button
                  onClick={onRenew}
                  className="mt-2 flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-md transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Renew Now</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
