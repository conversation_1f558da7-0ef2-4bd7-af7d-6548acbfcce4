// components/UserProfile/UserProfile.tsx
'use client'
import Image from 'next/image'
import { useRouter } from '@i18n/routing'
import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { clearUserData } from '@/utils/lib/user'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'

interface UserProfile {
  avatar: string
  email: string
  id: string
  points: number
  balance: number
}

interface UserProfileProps {
  user: UserProfile
}

export function UserProfileClient({ user }: UserProfileProps) {
  const t = useTranslations()
  const router = useRouter()
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const { isLoggedIn, isValidSubscriber, isBlacklisted, userInfo } =
    usePermissionCheck()
  // 添加续订相关状态
  const [showRenewDialog, setShowRenewDialog] = useState(false)
  const [isRenewing, setIsRenewing] = useState(false)
  const [renewError, setRenewError] = useState('')

  const onRenew = async () => {
    // 如果没有订阅时间，跳转到定价页面
    if (!userInfo?.membership_end_date) {
      router.push('/pricing')
      return
    }

    setShowRenewDialog(true)
  }

  const handleRenewConfirm = async () => {
    try {
      setIsRenewing(true)
      setRenewError('')

      const response = await fetch('/api/membership/renew', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reasonText: 'Customer requested renewal through website',
        }),
      })

      const data = await response.json()

      if (!data.checkoutUrl) {
        throw new Error(data.message || 'Failed to get renewal URL')
      }

      // 打开续订URL
      window.location.href = data.checkoutUrl

      // 关闭续订对话框
      setShowRenewDialog(false)

      // 可选：显示提示信息
      // toast.success('Please complete your renewal in the new window')
    } catch (error) {
      console.error('Renewal error:', error)
      setRenewError(
        error instanceof Error ? error.message : 'Failed to renew subscription'
      )
    } finally {
      setIsRenewing(false)
    }
  }

  useEffect(() => {
    if (localStorage.getItem('REDIRECT_PATH')) {
      router.push(localStorage.getItem('REDIRECT_PATH') as string)
      localStorage.removeItem('REDIRECT_PATH')
    }
  }, [])

  const handleSignOut = () => {
    try {
      // Clear all user data
      clearUserData()

      // Redirect to home page
      router.push('/')
      router.refresh()
    } catch (error) {
      console.error('Error during sign out:', error)
    }
  }

  return (
    <div className="  relative overflow-hidden ">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div
          className="absolute top-3/4 right-1/4 w-80 h-80 bg-fuchsia-500/10 rounded-full blur-3xl animate-pulse"
          style={{ animationDelay: '2s' }}
        ></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-violet-500/5 rounded-full blur-2xl"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 py-8 lg:py-2 max-w-7xl mt-2">
        {/* 页面标题区域 */}
        <div className="text-center mb-8 lg:mb-12 mt-10">
          <div className="inline-flex items-center gap-3 bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl px-6 py-3 mb-6">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-fuchsia-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-xl">👤</span>
            </div>
            <h1 className="text-2xl lg:text-3xl font-bold text-white">
              {t('userProfile.title')}
            </h1>
          </div>
          <p className="text-gray-400 text-lg">{t('userProfile.subtitle')}</p>
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 lg:gap-10">
          {/* 左侧：用户信息卡片 */}
          <div className="xl:col-span-1">
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-3xl p-6 lg:p-8 h-fit sticky top-8">
              {/* 头像和基本信息 */}
              <div className="text-center mb-8">
                <div className="relative inline-block">
                  <div className="w-24 h-24 rounded-full lg:w-28 lg:h-28  ring-4 ring-purple-500/30 mx-auto mb-4 flex items-center justify-center bg-gradient-to-br from-purple-500/20 to-fuchsia-500/20 border border-purple-400/30 overflow-hidden">
                    {user.avatar ? (
                      <Image
                        src={user.avatar}
                        alt="Profile"
                        fill
                        className="object-cover rounded-full"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <svg
                          className="w-12 h-12 lg:w-14 lg:h-14 text-purple-300"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full border-4 border-white/10 flex items-center justify-center">
                    <div className="w-3 h-3 bg-white rounded-full"></div>
                  </div>
                </div>
                <h2 className="text-xl lg:text-2xl font-bold text-white mb-2">
                  {userInfo?.username || 'Welcome'}
                </h2>
                <p className="text-gray-400 text-sm break-all">{user.email}</p>
              </div>

              {/* 会员状态卡片 */}
              <div className="bg-gradient-to-br from-purple-500/20 to-fuchsia-500/20 border border-purple-400/30 rounded-2xl p-4 mb-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-purple-300 font-medium">
                    {t('userProfile.membershipStatus')}
                  </span>
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      isValidSubscriber()
                        ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                        : 'bg-orange-500/20 text-orange-300 border border-orange-500/30'
                    }`}
                  >
                    {isValidSubscriber()
                      ? t('userProfile.validMember')
                      : t('userProfile.nonMember')}
                  </span>
                </div>
                {userInfo?.membership_end_date && (
                  <p className="text-gray-300 text-sm">
                    {t('userProfile.expirationTime')}:{' '}
                    {new Date(
                      userInfo.membership_end_date
                    ).toLocaleDateString()}
                  </p>
                )}
              </div>

              {/* 积分显示 */}
              <div className="bg-gradient-to-br from-violet-500/20 to-purple-500/20 border border-violet-400/30 rounded-2xl p-4 mb-6">
                <div className="flex items-center justify-between">
                  <span className="text-violet-300 font-medium">
                    {t('userProfile.currentPoints')}
                  </span>
                  <span className="text-2xl font-bold text-white">
                    {userInfo?.points || 0}
                  </span>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="space-y-3">
                {userInfo && (
                  <button
                    onClick={onRenew}
                    className="w-full bg-gradient-to-r from-purple-600 to-fuchsia-600 hover:from-purple-700 hover:to-fuchsia-700 text-white px-4 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl"
                  >
                    {userInfo.membership_end_date
                      ? t('userProfile.renewMembership')
                      : t('userProfile.subscribeMembership')}
                  </button>
                )}
                <button
                  onClick={() => setShowConfirmDialog(true)}
                  className="w-full bg-white/5 hover:bg-white/10 border border-white/20 hover:border-red-400/50 text-gray-300 hover:text-red-300 px-4 py-3 rounded-xl font-medium transition-all duration-300"
                >
                  {t('userProfile.signOut')}
                </button>
              </div>
            </div>
          </div>

          {/* 右侧：详细信息区域 */}
          <div className="xl:col-span-2 space-y-8">
            {/* 账户状态总览 */}
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-3xl p-6 lg:p-8">
              <h3 className="text-xl lg:text-2xl font-bold text-white mb-6 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">📊</span>
                </div>
                {t('userProfile.accountStatus')}
              </h3>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-white/5 border border-white/10 rounded-2xl p-4 text-center">
                  <div
                    className={`w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center ${
                      isLoggedIn
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-red-500/20 text-red-400'
                    }`}
                  >
                    {isLoggedIn ? '✓' : '✗'}
                  </div>
                  <p className="text-gray-400 text-sm mb-1">
                    {t('userProfile.loginStatus')}
                  </p>
                  <p className="text-white font-medium">
                    {isLoggedIn
                      ? t('userProfile.loggedIn')
                      : t('userProfile.notLoggedIn')}
                  </p>
                </div>

                <div className="bg-white/5 border border-white/10 rounded-2xl p-4 text-center">
                  <div
                    className={`w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center ${
                      isValidSubscriber()
                        ? 'bg-green-500/20 text-green-400'
                        : 'bg-orange-500/20 text-orange-400'
                    }`}
                  >
                    👑
                  </div>
                  <p className="text-gray-400 text-sm mb-1">
                    {t('userProfile.membershipStatus')}
                  </p>
                  <p className="text-white font-medium">
                    {isValidSubscriber()
                      ? t('userProfile.validMember')
                      : t('userProfile.nonMember')}
                  </p>
                </div>

                <div className="bg-white/5 border border-white/10 rounded-2xl p-4 text-center">
                  <div className="w-12 h-12 mx-auto mb-3 bg-purple-500/20 text-purple-400 rounded-full flex items-center justify-center">
                    💎
                  </div>
                  <p className="text-gray-400 text-sm mb-1">
                    {t('userProfile.pointsBalance')}
                  </p>
                  <p className="text-white font-medium">
                    {userInfo?.points || 0}
                  </p>
                </div>

                <div className="bg-white/5 border border-white/10 rounded-2xl p-4 text-center">
                  <div
                    className={`w-12 h-12 mx-auto mb-3 rounded-full flex items-center justify-center ${
                      isBlacklisted()
                        ? 'bg-red-500/20 text-red-400'
                        : 'bg-green-500/20 text-green-400'
                    }`}
                  >
                    🛡️
                  </div>
                  <p className="text-gray-400 text-sm mb-1">
                    {t('userProfile.accountStatus')}
                  </p>
                  <p className="text-white font-medium">
                    {isBlacklisted()
                      ? t('userProfile.restricted')
                      : t('userProfile.normal')}
                  </p>
                </div>
              </div>
            </div>

            {/* 用户详细信息 */}
            <div className="bg-white/5 backdrop-blur-lg border border-white/10 rounded-3xl p-6 lg:p-8">
              <h3 className="text-xl lg:text-2xl font-bold text-white mb-6 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">📋</span>
                </div>
                {t('userProfile.detailedInfo')}
              </h3>

              {userInfo ? (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-white/10">
                      <span className="text-gray-400">
                        {t('userProfile.username')}
                      </span>
                      <span className="text-white font-medium">
                        {userInfo.username}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-white/10">
                      <span className="text-gray-400">
                        {t('userProfile.email')}
                      </span>
                      <span className="text-white font-medium break-all">
                        {userInfo.email}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-white/10">
                      <span className="text-gray-400">
                        {t('userProfile.membershipLevel')}
                      </span>
                      <span className="text-purple-300 font-medium">
                        {userInfo.membership_level}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-3 border-b border-white/10">
                      <span className="text-gray-400">
                        {t('userProfile.membershipStatus')}
                      </span>
                      <span className="text-fuchsia-300 font-medium">
                        {userInfo.membership_status}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-white/10">
                      <span className="text-gray-400">
                        {t('userProfile.registrationTime')}
                      </span>
                      <span className="text-white font-medium">--</span>
                    </div>
                    <div className="flex justify-between items-center py-3 border-b border-white/10">
                      <span className="text-gray-400">
                        {t('userProfile.lastLogin')}
                      </span>
                      <span className="text-white font-medium">
                        {t('userProfile.justNow')}
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-500/20 rounded-full mx-auto mb-4 flex items-center justify-center">
                    <span className="text-gray-400 text-2xl">👤</span>
                  </div>
                  <p className="text-gray-400 text-lg">
                    {t('userProfile.pleaseLoginFirst')}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* 确认对话框 */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm px-4">
          <div className="bg-slate-800 border border-slate-600/50 p-6 rounded-xl max-w-sm w-full shadow-2xl">
            <h3 className="text-xl font-semibold mb-4 text-white">
              {t('userProfile.confirmSignOutText')}
            </h3>
            <p className="text-gray-300 mb-6">
              {t('userProfile.confirmSignOut')}
            </p>
            <div className="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4">
              <button
                onClick={() => setShowConfirmDialog(false)}
                className="px-4 py-2 text-gray-300 hover:text-white transition-colors border border-slate-600 rounded-lg hover:border-slate-500"
              >
                {t('common.confirmation.cancel')}
              </button>
              <button
                onClick={() => {
                  handleSignOut()
                  setShowConfirmDialog(false)
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
              >
                {t('userProfile.signOut')}
              </button>
            </div>
          </div>
        </div>
      )}
      {/* 续订确认对话框 */}
      {showRenewDialog && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 backdrop-blur-sm px-4">
          <div className="bg-slate-800 border border-slate-600/50 p-6 rounded-xl max-w-sm w-full shadow-2xl">
            <h3 className="text-xl font-semibold mb-4 text-white">
              {t('common.confirmRenewalText')}
            </h3>
            <p className="text-gray-300 mb-6">{t('common.confirmRenewal')}</p>
            {renewError && (
              <p className="text-red-400 mb-4 text-sm bg-red-500/10 p-3 rounded-lg border border-red-500/30">
                {renewError}
              </p>
            )}
            <div className="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4">
              <button
                onClick={() => {
                  setShowRenewDialog(false)
                  setRenewError('')
                }}
                disabled={isRenewing}
                className="px-4 py-2 text-gray-300 hover:text-white transition-colors disabled:opacity-50 border border-slate-600 rounded-lg hover:border-slate-500 disabled:hover:border-slate-600"
              >
                {t('common.confirmation.cancel')}
              </button>
              <button
                onClick={handleRenewConfirm}
                disabled={isRenewing}
                className="px-4 py-2 bg-gradient-to-r from-purple-600 to-fuchsia-600 text-white rounded-lg hover:from-purple-700 hover:to-fuchsia-700 transition-colors disabled:opacity-50 flex items-center justify-center gap-2"
              >
                {isRenewing && (
                  <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    />
                  </svg>
                )}
                <span>
                  {isRenewing
                    ? t('common.processing')
                    : t('common.confirmRenewalButton')}
                </span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
