export default function TestTailwindPage() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Tailwind CSS 数值类测试页面
        </h1>

        {/* 测试标准数值类 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">标准数值类测试</h2>
          <div className="space-y-4">
            <div className="mt-4 p-4 bg-blue-100 rounded">mt-4 (margin-top: 1rem)</div>
            <div className="mt-8 p-4 bg-green-100 rounded">mt-8 (margin-top: 2rem)</div>
            <div className="mt-12 p-4 bg-yellow-100 rounded">mt-12 (margin-top: 3rem)</div>
            <div className="mt-16 p-4 bg-red-100 rounded">mt-16 (margin-top: 4rem)</div>
            <div className="mt-20 p-4 bg-purple-100 rounded">mt-20 (margin-top: 5rem)</div>
            <div className="mt-24 p-4 bg-pink-100 rounded">mt-24 (margin-top: 6rem)</div>
            <div className="mt-28 p-4 bg-indigo-100 rounded">mt-28 (margin-top: 7rem)</div>
            <div className="mt-32 p-4 bg-gray-100 rounded">mt-32 (margin-top: 8rem)</div>
          </div>
        </div>

        {/* 测试任意值类 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">任意值类测试</h2>
          <div className="space-y-4">
            <div className="mt-[75px] p-4 bg-blue-100 rounded">mt-[75px] (margin-top: 75px)</div>
            <div className="mt-[100px] p-4 bg-green-100 rounded">mt-[100px] (margin-top: 100px)</div>
            <div className="pt-[50px] p-4 bg-yellow-100 rounded">pt-[50px] (padding-top: 50px)</div>
            <div className="w-[200px] h-[100px] bg-red-100 rounded flex items-center justify-center">
              w-[200px] h-[100px]
            </div>
          </div>
        </div>

        {/* 测试其他数值类 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">其他数值类测试</h2>
          <div className="space-y-4">
            <div className="text-[24px] font-bold text-blue-600">text-[24px] - 自定义字体大小</div>
            <div className="bg-[#ff6b6b] text-white p-4 rounded">bg-[#ff6b6b] - 自定义背景色</div>
            <div className="border-[3px] border-[#4ecdc4] p-4 rounded">border-[3px] border-[#4ecdc4] - 自定义边框</div>
            <div className="rounded-[25px] bg-gradient-to-r from-purple-400 to-pink-400 text-white p-4">
              rounded-[25px] - 自定义圆角
            </div>
          </div>
        </div>

        {/* 测试响应式数值类 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">响应式数值类测试</h2>
          <div className="mt-4 md:mt-8 lg:mt-12 xl:mt-16 p-4 bg-gradient-to-r from-blue-400 to-purple-500 text-white rounded">
            响应式边距: mt-4 md:mt-8 lg:mt-12 xl:mt-16
          </div>
        </div>

        {/* 测试状态变体 */}
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">状态变体测试</h2>
          <button className="mt-4 hover:mt-2 transition-all duration-300 bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg">
            悬停时改变边距 (hover:mt-2)
          </button>
        </div>
      </div>
    </div>
  )
}
