'use client'

import React, { useState } from 'react'
import Image from 'next/image'

const ImageCarousel = () => {
  const [activeIndex, setActiveIndex] = useState(0)

  const images = [
    {
      src: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/cta-section.jpeg',
      alt: 'AI IMAGE example 0',
    },
    {
      src: '/gulika/girl-2.jpeg',
      alt: 'AI IMAGE example 1',
    },
    {
      src: '/gulika/powerpuff-generated.jpeg',
      alt: 'AI IMAGE example 2',
    },
    {
      src: '/gulika/ghibli-generated.jpeg',
      alt: 'AI IMAGE example 3',
    },
  ]

  const handleImageClick = (index: number) => {
    setActiveIndex(index)
  }

  return (
    <div className="relative aspect-square max-w-lg mx-auto">
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full perspective-1000">
        {images.map((image, index) => (
          <div
            key={index}
            className={`absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[85%] h-[85%] bg-white rounded-lg shadow-2xl transition-all duration-500 cursor-pointer
              ${
                index === activeIndex
                  ? 'rotate-0 scale-105 z-30'
                  : index === (activeIndex + 1) % 3
                  ? '-rotate-6 z-20'
                  : 'rotate-12 z-10'
              }`}
            onClick={() => handleImageClick(index)}
          >
            <div className="w-full h-full relative rounded-lg overflow-hidden border border-gray-200">
              <Image
                src={image.src}
                alt={image.alt}
                fill
                className="object-cover"
              />
            </div>
          </div>
        ))}

        {/* 装饰性元素：光影效果 */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full h-full bg-gradient-to-br from-blue-500 to-purple-500 rounded-full opacity-10 blur-3xl -z-10"></div>
      </div>
    </div>
  )
}

export default ImageCarousel
