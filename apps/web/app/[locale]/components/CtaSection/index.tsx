import React from 'react'
import { getTranslations } from 'next-intl/server'
import ImageCarousel from './ImageCarousel'
import { Link } from '@i18n/routing'

const CtaSection = async () => {
  const t = await getTranslations()

  return (
    <section className="py-24 relative overflow-hidden">
      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 overflow-hidden">
        {/* 顶部纯色遮罩 */}
        <div className="absolute top-0 left-0 right-0 h-10 bg-[#171124]/20 z-10 pointer-events-none" />
        <div className="relative h-full w-full bg-[#171124]">
          <div className="absolute bottom-0 left-0 right-0 top-0 bg-[linear-gradient(to_right,#ffffff1a_1px,transparent_1px),linear-gradient(to_bottom,#ffffff1a_1px,transparent_1px)] bg-[size:20px_20px] "></div>
          <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(125%_125%_at_50%_70%,rgba(99,102,241,0.3)_40%,rgba(17,23,36,1)_100%)]"></div>
          <div className="absolute bottom-0 left-0 right-0 top-0 bg-[radial-gradient(circle_at_center,transparent_30%,#171124_70%)]"></div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* 标题部分 */}
        <div className="text-center mb-16">
          <h2 className="text-4xl text-indigo-400 md:text-5xl font-bold !leading-[1.3] max-w-4xl mx-auto flex flex-col gap-2 pb-2">
            {t('home-cta.headline')}
          </h2>
        </div>

        {/* 内容部分 */}
        <div className="flex flex-col lg:flex-row items-center gap-12">
          {/* 左侧图像部分 */}
          <div className="w-full lg:w-1/2">
            <ImageCarousel />
          </div>

          {/* 右侧文字部分 */}
          <div className="w-full lg:w-1/2 text-center lg:text-left">
            <p className="text-xl text-gray-200 mb-10 leading-relaxed max-w-xl mx-auto lg:mx-0">
              {t('home-cta.description')}
            </p>

            <div className="inline-block mb-10">
              <Link href={'/tools/ai-art-generator'}>
                <div className="group/button relative px-10 py-5 bg-gradient-to-r  from-purple-500 to-pink-500  hover:opacity-90 rounded-lg cursor-pointer text-white font-medium text-lg inline-block shadow-lg shadow-blue-500/30 hover:shadow-xl hover:shadow-blue-500/40 transform hover:-translate-y-1 transition-all duration-300 overflow-hidden">
                  <span className="relative z-10">
                    {t('HomeCTA.getStarted')}
                  </span>
                  <div className="absolute inset-0 flex h-full w-full justify-center [transform:skew(-13deg)_translateX(-100%)] group-hover/button:duration-1000 group-hover/button:[transform:skew(-13deg)_translateX(100%)] pointer-events-none">
                    <div className="relative h-full w-10 bg-white/20"></div>
                  </div>
                </div>
              </Link>
            </div>

            {/* 社会证明 */}
            <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 text-gray-300">
              <div className="flex -space-x-2">
                {['Jess', 'Kevin', 'Maria', 'Liam', 'Alyssa'].map((name) => (
                  <div
                    key={name}
                    className="w-8 h-8 rounded-full border-2 border-gray-200 overflow-hidden"
                  >
                    <img
                      src={`https://api.dicebear.com/7.x/avataaars/svg?seed=${name}`}
                      alt={`${name}'s avatar`}
                      className="w-full h-full"
                    />
                  </div>
                ))}
              </div>
              <p className="text-sm">
                {t('HomeCTA.joinedBy')}{' '}
                <span className="text-white font-medium">
                  {t('HomeCTA.userCount')}
                </span>{' '}
                {t('HomeCTA.worldwide')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default CtaSection
