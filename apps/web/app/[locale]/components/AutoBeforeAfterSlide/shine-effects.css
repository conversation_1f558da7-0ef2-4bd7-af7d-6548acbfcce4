/* 星星闪烁动画 */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

/* 光效脉冲动画 */
@keyframes shine-pulse {
  0%, 100% {
    opacity: 0.2;
    transform: scaleY(0.8);
  }
  50% {
    opacity: 0.6;
    transform: scaleY(1.2);
  }
}

.animate-shine-pulse {
  animation: shine-pulse 1.5s ease-in-out infinite;
}

/* 光线扫过效果 */
@keyframes light-sweep {
  0% {
    transform: translateX(-100%) scaleX(0);
    opacity: 0;
  }
  50% {
    transform: translateX(0%) scaleX(1);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) scaleX(0);
    opacity: 0;
  }
}

.animate-light-sweep {
  animation: light-sweep 3s ease-in-out infinite;
}

/* 星星轨迹动画 */
@keyframes star-trail {
  0% {
    opacity: 0;
    transform: translateY(10px) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translateY(0px) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px) scale(0.5);
  }
}

.animate-star-trail {
  animation: star-trail 2.5s ease-in-out infinite;
}
