@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  :root {
    --background: 0 0% 98%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 217 91% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.75rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}


@layer components {
  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Glass effect */
  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Smooth transitions */
  .smooth-transition {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Canvas container styling */
  .canvas-container {
    background:
      radial-gradient(circle at 20px 20px, rgba(0,0,0,0.05) 1px, transparent 1px),
      radial-gradient(circle at 60px 60px, rgba(0,0,0,0.05) 1px, transparent 1px);
    background-size: 80px 80px;
    background-position: 0 0, 40px 40px;
  }

  /* Upload page animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);
  }

  .animate-fade-in-up.delay-200 {
    animation-delay: 0.2s;
  }

  .animate-fade-in-up.delay-300 {
    animation-delay: 0.3s;
  }

  .animate-spin-slow {
    animation: spin 3s linear infinite;
  }

  .hover\:scale-102:hover {
    transform: scale(1.02);
  }

  @keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-float.delay-1000 {
    animation-delay: 1s;
  }

  .animate-float.delay-2000 {
    animation-delay: 2s;
  }

  /* Instructions tooltip animations */
  .animate-in {
    animation-fill-mode: both;
  }

  .slide-in-from-top-2 {
    animation: slideInFromTop 0.3s ease-out;
  }

  @keyframes slideInFromTop {
    from {
      opacity: 0;
      transform: translateY(-8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Pulse animation for help icon */
  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* Bounce animation with delay */
  .animate-bounce.delay-100 {
    animation-delay: 0.1s;
  }

  .animate-bounce.delay-200 {
    animation-delay: 0.2s;
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  body[data-scroll-locked] {
    overflow-y: auto !important;
  }
}

pre.shiki {
  @apply mb-4 rounded-lg p-6;
}

@keyframes shine-slow {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes shine-fast {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(100%);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.8;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(0.9);
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(100%) translateX(0);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100%) translateX(20px);
    opacity: 0;
  }
}

.animate-shine-slow {
  animation: shine-slow 3s infinite;
}

.animate-shine-fast {
  animation: shine-fast 2s infinite;
}

.animate-glow {
  animation: glow 2s infinite;
}

.particles-container .particle {
  position: absolute;
  bottom: 0;
  left: 0;
}

.particles-container .particle:nth-child(1) {
  left: 10%;
}
.particles-container .particle:nth-child(2) {
  left: 30%;
}
.particles-container .particle:nth-child(3) {
  left: 50%;
}
.particles-container .particle:nth-child(4) {
  left: 70%;
}
.particles-container .particle:nth-child(5) {
  left: 90%;
}

@keyframes loadingText {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

.animate-loading {
  animation: loadingText 1.5s infinite;
}

/* 在您的 CSS 文件中添加以下内容 */
@keyframes floating {
  0% {
    transform: translateY(0) rotate(0deg);
    background-position: 0% 50%;
  }
  25% {
    transform: translateY(-3px) rotate(0.3deg);
    background-position: 50% 50%;
  }
  50% {
    transform: translateY(0) rotate(0deg);
    background-position: 100% 50%;
  }
  75% {
    transform: translateY(3px) rotate(-0.3deg);
    background-position: 50% 50%;
  }
  100% {
    transform: translateY(0) rotate(0deg);
    background-position: 0% 50%;
  }
}

.animate-floating {
  animation: floating 4s ease-in-out infinite;
  background-size: 200% auto;
}

/* app/globals.css 或者其他全局CSS文件 */
@keyframes blob {
  0% {
    transform: scale(1);
  }
  33% {
    transform: scale(1.1) translateY(-10px);
  }
  66% {
    transform: scale(0.9) translateX(10px);
  }
  100% {
    transform: scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite alternate;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Hover放大效果容器 - 防止元素超出父容器 */
.hover-scale-container {
  padding: 1rem; /* 为放大效果提供空间 */
}

.hover-scale-container > * {
  transition: transform 0.3s ease;
}

.hover-scale-container > *:hover {
  transform: scale(1.05);
}

/* 更小的放大效果 */
.hover-scale-container-sm {
  padding: 0.5rem;
}

.hover-scale-container-sm > * {
  transition: transform 0.3s ease;
}

.hover-scale-container-sm > *:hover {
  transform: scale(1.02);
}

/* 渐变动画效果 */
@keyframes gradient-xy {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-xy {
  animation: gradient-xy 15s ease infinite;
}

/* 更大的放大效果 */
.hover-scale-container-lg {
  padding: 1.5rem;
}

.hover-scale-container-lg > * {
  transition: transform 0.3s ease;
}

.hover-scale-container-lg > *:hover {
  transform: scale(1.1);
}

/* 图片hover放大效果 */
.image-hover-scale {
  transition: transform 0.3s ease;
  overflow: hidden;
}

.image-hover-scale:hover {
  transform: scale(1.05);
}

.image-hover-scale img {
  transition: transform 0.3s ease;
}

.image-hover-scale:hover img {
  transform: scale(1.1);
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #c9c9c9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #c9c9c9;
}
