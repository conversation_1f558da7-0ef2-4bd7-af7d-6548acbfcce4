import { Mail } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
  const t = await getTranslations()
  return {
    title: t('seo-contact-t'),
    description: t('seo-contact-d'),
    keywords: t('seo-contact-k'),
  }
}

export default async function PricingPage() {
  const t = await getTranslations()

  return (
    <main className="px-8 pt-40 pb-24 relative bg-gradient-to-r from-[#1a0033] via-[#0a001a] to-[#1a0033] min-h-screen text-white">
      {/* 装饰性背景元素 */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-purple-500 opacity-10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-20 -left-20 w-72 h-72 bg-fuchsia-500 opacity-10 rounded-full blur-3xl"></div>

        {/* 网格装饰 */}
        <div className="absolute inset-0 opacity-[0.05]">
          <div
            className="h-full w-full"
            style={{
              backgroundImage:
                'linear-gradient(rgba(168, 85, 247, 0.3) 1px, transparent 1px), linear-gradient(to right, rgba(168, 85, 247, 0.3) 1px, transparent 1px)',
              backgroundSize: '32px 32px',
            }}
          ></div>
        </div>
      </div>

      <section className="py-12 md:py-16 relative z-10 mt-[240px]">
        <div className="max-w-7xl mx-auto px-4">
          <div className="max-w-2xl">
            <div className="mb-6">
              <div className="inline-block p-3 rounded-xl bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 mb-4">
                <Mail className="w-6 h-6 text-purple-400" />
              </div>

              <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 via-fuchsia-400 to-indigo-400 bg-clip-text text-transparent">
                {t('contact.title')}
              </h2>

              <p className="text-lg text-gray-300 leading-relaxed mb-8">
                {t('contact.description')}
              </p>
            </div>

            <div className="space-x-4 flex items-center">
              <h3 className="text-gray-300">{t('contact.email.label')}</h3>
              <a
                href="mailto:<EMAIL>"
                className="text-lg text-white hover:text-purple-400 transition-colors font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
