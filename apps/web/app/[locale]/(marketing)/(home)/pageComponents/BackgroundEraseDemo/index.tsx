'use client'

import { useTranslations } from 'next-intl'
import BeforeAfterSlider from '@/[locale]/components/BeforeAfterSlide'

interface BackgroundEraseDemoProps {
  className?: string
}
// beforeImage="/images/home/<USER>" afterImage="/images/home/<USER>"
export function BackgroundEraseDemo({
  className = '',
}: BackgroundEraseDemoProps) {
  const t = useTranslations('home')
  return (
    <div className={`relative group ${className}`}>
      <BeforeAfterSlider
        className="w-full !h-80"
        beforeImage="/images/home/<USER>"
        afterImage="/images/home/<USER>"
        beforeAlt={t('beforeImageAlt')}
        afterAlt={t('afterImageAlt')}
      />

      {/* Description */}
      <div className="mt-4 text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('backgroundEraseDemoTitle')}
        </h3>
        <p className="text-sm text-gray-600">
          {t('backgroundEraseDemoDescription')}
        </p>
      </div>
    </div>
  )
}
