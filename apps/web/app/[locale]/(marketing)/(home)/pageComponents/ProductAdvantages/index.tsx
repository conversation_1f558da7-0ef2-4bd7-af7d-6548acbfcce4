'use client'

import { useState } from 'react'
import { ArrowRight, Shield, Zap, Cloud, Star, CheckCircle } from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Link } from '@i18n/routing'

// Simple wireframe elements around cards
const FloatingWireframes = ({
  cardIndex,
  isActive,
}: {
  cardIndex: number
  isActive: boolean
}) => {
  return (
    <>
      {cardIndex === 1 && (
        <div className="absolute -left-12 top-1/4 hidden lg:block">
          <svg
            width="40"
            height="80"
            viewBox="0 0 40 80"
            className={`transition-opacity duration-500 ${
              isActive ? 'opacity-60' : 'opacity-30'
            }`}
          >
            <path
              d="M10 20 Q20 40 10 60"
              stroke="#3B82F6"
              strokeWidth="1.5"
              fill="none"
              strokeDasharray="4 2"
            />
          </svg>
        </div>
      )}

      {cardIndex === 2 && (
        <div className="absolute -right-12 top-1/3 hidden lg:block">
          <svg
            width="40"
            height="60"
            viewBox="0 0 40 60"
            className={`transition-opacity duration-500 ${
              isActive ? 'opacity-60' : 'opacity-30'
            }`}
          >
            <path
              d="M30 15 Q20 30 30 45"
              stroke="#8B5CF6"
              strokeWidth="1.5"
              fill="none"
              strokeDasharray="3 2"
            />
          </svg>
        </div>
      )}
    </>
  )
}

interface ProductAdvantagesProps {
  link?: string
}

export function ProductAdvantages({
  link = '/playground',
}: ProductAdvantagesProps) {
  const t = useTranslations('home')
  const [hoveredCard, setHoveredCard] = useState<number | null>(null)

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-slate-50 via-white to-indigo-50 relative overflow-hidden">
      {/* Minimal background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-20">
        {/* Subtle grid pattern */}
        <svg
          className="absolute top-0 left-0 w-full h-full"
          viewBox="0 0 1200 800"
        >
          <defs>
            <pattern
              id="grid"
              width="80"
              height="80"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 80 0 L 0 0 0 80"
                fill="none"
                stroke="#E2E8F0"
                strokeWidth="0.5"
                opacity="0.3"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      <div className="max-w-7xl mx-auto relative">
        {/* Section Header */}
        <div className="text-center mb-20 relative">
          {/* Simple top decoration */}
          <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 hidden lg:block">
            <div className="w-12 h-0.5 bg-gradient-to-r from-blue-400 to-purple-400 opacity-50"></div>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('productAdvantagesTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            {t('productAdvantagesDescription')}
          </p>
        </div>

        {/* Advantages Grid */}
        <div className="grid lg:grid-cols-2 gap-16 mb-20 relative">
          {/* First Advantage */}
          <div
            className="group relative"
            onMouseEnter={() => setHoveredCard(1)}
            onMouseLeave={() => setHoveredCard(null)}
          >
            {/* Floating wireframes around the card */}
            <FloatingWireframes cardIndex={1} isActive={hoveredCard === 1} />

            <div className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-blue-200 relative z-10">
              <div className="flex items-start gap-6">
                <div className="flex-shrink-0 max-md:hidden">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Shield className="w-8 h-8 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                    {t('advantage1Title')}
                  </h3>
                  <p className="text-gray-700 leading-relaxed mb-6">
                    {t('advantage1Description')}
                  </p>

                  {/* Clean features list */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-600">
                        {t('advantage1Feature1')}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-600">
                        {t('advantage1Feature2')}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-600">
                        {t('advantage1Feature3')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Simple hover animation */}
              <div
                className={`mt-6 overflow-hidden transition-all duration-500 ${
                  hoveredCard === 1
                    ? 'max-h-16 opacity-100'
                    : 'max-h-0 opacity-0'
                }`}
              >
                <div className="flex items-center gap-2 text-blue-600 font-medium bg-blue-50 rounded-lg p-3">
                  <Zap className="w-4 h-4" />
                  <span className="text-sm">{t('advantage1Hover')}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Second Advantage */}
          <div
            className="group relative"
            onMouseEnter={() => setHoveredCard(2)}
            onMouseLeave={() => setHoveredCard(null)}
          >
            {/* Floating wireframes around the card */}
            <FloatingWireframes cardIndex={2} isActive={hoveredCard === 2} />

            <div className="bg-white rounded-3xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 border border-gray-100 hover:border-purple-200 relative z-10">
              <div className="flex items-start gap-6">
                <div className="flex-shrink-0 max-md:hidden">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                    <Star className="w-8 h-8 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                    {t('advantage2Title')}
                  </h3>
                  <p className="text-gray-700 leading-relaxed mb-6">
                    {t('advantage2Description')}
                  </p>

                  {/* Clean features list */}
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-600">
                        {t('advantage2Feature1')}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-600">
                        {t('advantage2Feature2')}
                      </span>
                    </div>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="w-5 h-5 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-600">
                        {t('advantage2Feature3')}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Simple hover animation */}
              <div
                className={`mt-6 overflow-hidden transition-all duration-500 ${
                  hoveredCard === 2
                    ? 'max-h-16 opacity-100'
                    : 'max-h-0 opacity-0'
                }`}
              >
                <div className="flex items-center gap-2 text-purple-600 font-medium bg-purple-50 rounded-lg p-3">
                  <Cloud className="w-4 h-4" />
                  <span className="text-sm">{t('advantage2Hover')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center relative">
          <Link
            href={link}
            className="group w-fit bg-[#0f70e6] hover:!bg-[rgba(18,125,255)] text-white px-12 py-5 rounded-2xl text-xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl flex items-center gap-4 mx-auto relative overflow-hidden"
          >
            {/* Button shine effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>

            <span className="relative z-10">{t('tryForFreeNow')}</span>
            <ArrowRight className="w-6 h-6 group-hover:translate-x-1 transition-transform duration-300 relative z-10" />
          </Link>
          <p className="text-sm text-gray-500 mt-4">
            {t('noRegistrationRequired')}
          </p>
        </div>
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes dash {
          to {
            stroke-dashoffset: -30;
          }
        }

        @keyframes float {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @keyframes glow {
          0%,
          100% {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
          }
          50% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.8),
              0 0 30px rgba(59, 130, 246, 0.6);
          }
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        @keyframes ripple {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(4);
            opacity: 0;
          }
        }
      `}</style>
    </section>
  )
}
