'use client'

import { useTranslations } from 'next-intl'
import AutoBeforeAfterSlider from '@/[locale]/components/AutoBeforeAfterSlide'

interface ObjectRemovalDemoProps {
  className?: string
}

export function ObjectRemovalDemo({ className = '' }: ObjectRemovalDemoProps) {
  const t = useTranslations('home')

  return (
    <div className={`relative group ${className}`}>
      {/* Container */}
      <div className="relative w-full h-80 rounded-2xl overflow-hidden shadow-2xl bg-gray-100">
        {/* Before Image (always visible) */}
        <AutoBeforeAfterSlider
          hideLabel
          className="!w-full !h-full  !object-cover"
          beforeImage="/images/home/<USER>"
          afterImage="/images/home/<USER>"
          beforeAlt={t('beforeImageAlt')}
          afterAlt={t('afterImageAlt')}
        />
      </div>

      {/* Description */}
      <div className="mt-4 text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {t('objectRemovalDemoTitle')}
        </h3>
        <p className="text-sm text-gray-600">
          {t('objectRemovalDemoDescription')}
        </p>
      </div>
    </div>
  )
}
