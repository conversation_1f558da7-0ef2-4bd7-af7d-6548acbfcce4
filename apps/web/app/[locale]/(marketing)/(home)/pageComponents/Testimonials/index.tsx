'use client'

import { useState, useEffect } from 'react'
import {
  Star,
  Quote,
  User,
  Briefcase,
  Camera,
  ShoppingBag,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react'
import { useTranslations } from 'next-intl'

export function Testimonials() {
  const t = useTranslations('home')
  const [hoveredTestimonial, setHoveredTestimonial] = useState<number | null>(
    null
  )
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  const testimonials = [
    {
      id: 1,
      text: t('testimonial1'),
      author: t('testimonial1Author'),
      role: t('testimonial1Role'),
      avatar:
        'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=100&h=100&fit=crop&crop=face',
      icon: Camera,
      rating: 5,
      color: 'from-blue-500 to-cyan-600',
    },
    {
      id: 2,
      text: t('testimonial2'),
      author: t('testimonial2Author'),
      role: t('testimonial2Role'),
      avatar:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face',
      icon: User,
      rating: 5,
      color: 'from-purple-500 to-pink-600',
    },
    {
      id: 3,
      text: t('testimonial3'),
      author: t('testimonial3Author'),
      role: t('testimonial3Role'),
      avatar:
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face',
      icon: Camera,
      rating: 5,
      color: 'from-green-500 to-emerald-600',
    },
    {
      id: 4,
      text: t('testimonial4'),
      author: t('testimonial4Author'),
      role: t('testimonial4Role'),
      avatar:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
      icon: ShoppingBag,
      rating: 5,
      color: 'from-orange-500 to-red-600',
    },
  ]

  // Auto-play carousel
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % testimonials.length)
    }, 4000) // Change slide every 4 seconds

    return () => clearInterval(interval)
  }, [isAutoPlaying, testimonials.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % testimonials.length)
  }

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + testimonials.length) % testimonials.length
    )
  }

  const goToSlide = (index: number) => {
    setCurrentSlide(index)
  }

  return (
    <section className="py-20 px-4 bg-white">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('testimonialsTitle')}
          </h2>
          <div className="flex items-center justify-center gap-2 mb-4">
            {[...Array(5)].map((_, i) => (
              <Star key={i} className="w-8 h-8 text-yellow-400 fill-current" />
            ))}
            <span className="text-2xl font-bold text-gray-700 ml-2">
              {t('testimonialsRating')}
            </span>
          </div>
          <p className="text-lg text-gray-600">
            {t('testimonialsReviewsCount')}
          </p>
        </div>

        {/* Testimonials Carousel */}
        <div className="relative max-w-4xl mx-auto mb-16 max-md:hidden">
          {/* Carousel Container */}
          <div
            className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-slate-50 to-white shadow-2xl"
            onMouseEnter={() => setIsAutoPlaying(false)}
            onMouseLeave={() => setIsAutoPlaying(true)}
          >
            {/* Slides */}
            <div
              className="flex transition-transform duration-700 ease-in-out"
              style={{ transform: `translateX(-${currentSlide * 100}%)` }}
            >
              {testimonials.map((testimonial, index) => {
                const IconComponent = testimonial.icon

                return (
                  <div
                    key={testimonial.id}
                    className="w-full flex-shrink-0 p-12 text-center relative"
                  >
                    {/* Large Quote Icon */}
                    <div className="absolute top-8 left-8 w-16 h-16 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-full flex items-center justify-center">
                      <Quote className="w-8 h-8 text-blue-500/30" />
                    </div>

                    {/* Rating */}
                    <div className="flex items-center justify-center gap-1 mb-8">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star
                          key={i}
                          className="w-6 h-6 text-yellow-400 fill-current"
                        />
                      ))}
                    </div>

                    {/* Testimonial Text */}
                    <blockquote className="text-2xl lg:text-3xl font-medium text-gray-800 leading-relaxed mb-12 max-w-3xl mx-auto italic">
                      "{testimonial.text}"
                    </blockquote>

                    {/* Author Info */}
                    <div className="flex flex-col items-center">
                      <div className="relative mb-4">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.author}
                          className="w-20 h-20 rounded-full object-cover border-4 border-white shadow-xl"
                        />
                        <div
                          className={`absolute -bottom-2 -right-2 w-10 h-10 bg-gradient-to-r ${testimonial.color} rounded-full flex items-center justify-center shadow-lg`}
                        >
                          <IconComponent className="w-5 h-5 text-white" />
                        </div>
                      </div>
                      <div>
                        <p className="font-bold text-xl text-gray-900 mb-1">
                          {testimonial.author}
                        </p>
                        <p className="text-gray-600">{testimonial.role}</p>
                      </div>
                    </div>

                    {/* Background Decoration */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-br ${testimonial.color} opacity-5 rounded-3xl pointer-events-none`}
                    ></div>
                  </div>
                )
              })}
            </div>

            {/* Navigation Arrows */}
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 group"
            >
              <ChevronLeft className="w-6 h-6 text-gray-600 group-hover:text-blue-600" />
            </button>

            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 hover:bg-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300 hover:scale-110 group"
            >
              <ChevronRight className="w-6 h-6 text-gray-600 group-hover:text-blue-600" />
            </button>
          </div>

          {/* Dots Indicator */}
          {/* <div className="flex justify-center gap-3 mt-8">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  index === currentSlide
                    ? 'bg-blue-600 w-8'
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div> */}
        </div>

       <div className='grid md:grid-cols-2 lg:grid-cols-4 gap-8'>
       {testimonials.map((testimonial) => {
            const IconComponent = testimonial.icon
            const isHovered = hoveredTestimonial === testimonial.id

            return (
              <div
                key={testimonial.id}
                className="group relative bg-gradient-to-br from-gray-50 to-white rounded-3xl p-6 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 cursor-pointer"
                onMouseEnter={() => setHoveredTestimonial(testimonial.id)}
                onMouseLeave={() => setHoveredTestimonial(null)}
              >
                {/* Quote Icon */}
                <div className="absolute -top-3 -left-3 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg">
                  <Quote className="w-4 h-4 text-white" />
                </div>

                {/* Rating */}
                <div className="flex items-center gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-4 h-4 text-yellow-400 fill-current"
                    />
                  ))}
                </div>

                {/* Testimonial Text */}
                <p className="text-gray-700 leading-relaxed mb-6 text-sm">
                  "{testimonial.text}"
                </p>

                {/* Author Info */}
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.author}
                      className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-md"
                    />
                    <div
                      className={`absolute -bottom-1 -right-1 w-6 h-6 bg-gradient-to-r ${
                        testimonial.color
                      } rounded-full flex items-center justify-center shadow-md ${
                        isHovered ? 'scale-110' : ''
                      } transition-transform duration-300`}
                    >
                      <IconComponent className="w-3 h-3 text-white" />
                    </div>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900 text-sm">
                      {testimonial.author}
                    </p>
                    <p className="text-xs text-gray-500">{testimonial.role}</p>
                  </div>
                </div>

                {/* Hover Effect Background */}
                <div
                  className={`absolute inset-0 bg-gradient-to-br ${testimonial.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500 rounded-3xl pointer-events-none`}
                ></div>

                {/* Floating Animation */}
                <div
                  className={`absolute top-4 right-4 transition-all duration-300 ${
                    isHovered ? 'scale-110 rotate-12' : ''
                  }`}
                >
                  <div className="w-8 h-8 bg-white/80 rounded-full flex items-center justify-center shadow-md">
                    <IconComponent
                      className={`w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-300`}
                    />
                  </div>
                </div>
              </div>
            )
          })}
       </div>
        {/* Stats Section */}
        <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">10M+</div>
            <div className="text-sm text-gray-600">{t('imagesProcessed')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">50%</div>
            <div className="text-sm text-gray-600">{t('fasterWorkflow')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">4.9★</div>
            <div className="text-sm text-gray-600">{t('userRating')}</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">24/7</div>
            <div className="text-sm text-gray-600">{t('available')}</div>
          </div>
        </div>
      </div>
    </section>
  )
}
