'use client'

import type { ReactNode } from 'react'
import { useRef } from 'react'

import {
  ArrowR<PERSON>,
  Play,
  ShoppingBag,
  Users,
  Camera,
  Star,
} from 'lucide-react'
import { useTranslations } from 'next-intl'
import AutoBeforeAfterSlider from '@/[locale]/components/AutoBeforeAfterSlide'
import { Link } from '@i18n/routing'

// Type definition for use case item
interface UseCase {
  id: number
  icon: React.ComponentType<any>
  title: string
  description: string
  cta: string
  image?: string
  stats: string
  isVideo?: boolean
  color: string
  renderContent?: ReactNode // 自定义渲染内容
}

// Simple wireframe elements around use case cards
interface UseCasesProps {
  link?: string
}

export function UseCases({ link = '/playground' }: UseCasesProps) {
  const t = useTranslations('home')

  // 视频引用管理
  const videoRefs = useRef<{ [key: number]: HTMLVideoElement | null }>({})

  const useCases: UseCase[] = [
    {
      id: 1,
      icon: Play,
      title: t('useCase1Title'),
      description: t('useCase1Description'),
      cta: t('useCase1CTA'),
      image: 'https://www.media.io/video/video2.mp4',
      stats: t('useCase1Stats'),
      isVideo: true,
      color: 'from-red-500 to-pink-600',
    },
    {
      id: 2,
      icon: ShoppingBag,
      title: t('useCase2Title'),
      description: t('useCase2Description'),
      cta: t('useCase2CTA'),
      image: '/videos/home/<USER>',
      isVideo: true,
      stats: t('useCase2Stats'),
      color: 'from-blue-500 to-cyan-600',
    },
    {
      id: 3,
      icon: Users,
      title: t('useCase3Title'),
      description: t('useCase3Description'),
      cta: t('useCase3CTA'),
      stats: t('useCase3Stats'),
      color: 'from-green-500 to-emerald-600',
      renderContent: (
        <AutoBeforeAfterSlider
          // showShine
          hideLabel
          className="!w-full !h-64 md:!h-72 lg:!h-72 !object-cover"
          beforeImage="/images/home/<USER>"
          afterImage="/images/home/<USER>"
          beforeAlt={t('useCaseBgBlurPortraitAlt')}
          afterAlt={t('useCaseBgBlurPortraitAlt')}
        />
      ),
    },
    {
      id: 4,
      icon: Camera,
      title: t('useCase4Title'),
      description: t('useCase4Description'),
      cta: t('useCase4CTA'),
      image:
        'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=600&h=400&fit=crop',
      stats: t('useCase4Stats'),
      color: 'from-purple-500 to-indigo-600',
      // 示例：自定义渲染内容
      renderContent: (
        <AutoBeforeAfterSlider
          hideLabel
          className="!w-full !h-64 md:!h-72 lg:!h-72 !object-cover"
          beforeImage="/images/home/<USER>"
          afterImage="/images/home/<USER>"
          beforeAlt={t('useCaseBgBlurPortraitAlt')}
          afterAlt={t('useCaseBgBlurPortraitAlt')}
        />
      ),
    },
  ]

  // 获取图片的 alt 文本
  const getImageAlt = (useCaseId: number) => {
    switch (useCaseId) {
      case 1:
        return t('useCaseTiktokWatermarkRemoverAlt')
      case 2:
        return t('useCaseImageCutoutBackgroundAlt')
      case 3:
        return t('useCaseRemovePeopleFromPhotosAlt')
      case 4:
        return t('useCaseBgBlurPortraitAlt')
      default:
        return t('exampleImageAlt')
    }
  }

  return (
    <section className="py-20 px-4 bg-gradient-to-br from-gray-50 via-white to-slate-50 relative overflow-hidden">
      {/* Minimal background */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none opacity-15">
        {/* Very subtle grid pattern */}
        <svg
          className="absolute top-0 left-0 w-full h-full"
          viewBox="0 0 1400 1000"
        >
          <defs>
            <pattern
              id="usecases-grid"
              width="100"
              height="100"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 100 0 L 0 0 0 100"
                fill="none"
                stroke="#CBD5E1"
                strokeWidth="0.5"
                opacity="0.2"
              />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#usecases-grid)" />
        </svg>
      </div>

      <div className="max-w-7xl mx-auto relative">
        {/* Section Header */}
        <div className="text-center mb-20 relative">
          {/* Simple top decoration */}
          <div className="absolute -top-6 left-1/2 transform -translate-x-1/2 hidden lg:block">
            <div className="w-16 h-0.5 bg-gradient-to-r from-blue-400 via-purple-400 to-green-400 opacity-50"></div>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('useCasesTitle')}
          </h2>
          <p className="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            {t('useCasesDescription')}
          </p>
        </div>

        {/* Tab Content - reworked as grid on md+ screens to show titles/descriptions */}
        <div>
          <div className="grid grid-cols-1 gap-8 md:gap-10">
            {useCases.map((useCase, index) => {
              const IconComponent = useCase.icon

              return (
                <div
                  key={useCase.id}
                  className={`group relative bg-gray-50 rounded-[28px] hover:bg-gray-100 transition-all duration-300 border-0 flex flex-col md:flex-row md:items-center ${
                    index % 2 === 1 ? 'md:flex-row-reverse' : ''
                  } py-8 md:py-12`}
                >
                  {/* Media Collage */}
                  <div className="relative md:w-3/6 lg:w-3/6 p-8 md:p-12 flex items-center justify-center">
                    <div className="relative w-full max-w-xl">
                      {/* Background decorative circles */}
                      <div className="absolute -top-8 -left-8 w-32 h-32 bg-gradient-to-br from-cyan-200 to-cyan-300 rounded-full opacity-80"></div>
                      <div className="absolute -bottom-10 -right-10 w-40 h-40 bg-gradient-to-br from-amber-200 to-amber-300 rounded-full opacity-70"></div>

                      {/* Main media container */}
                      <div className="relative z-10">
                        {/* Primary media (larger) */}
                        <div className="relative z-20 w-full h-72 rounded-2xl overflow-hidden shadow-lg mb-8">
                          {useCase.renderContent ? (
                            <div className="relative w-full h-full">
                              {useCase.renderContent}
                            </div>
                          ) : useCase.isVideo ? (
                            <video
                              ref={(el) => {
                                videoRefs.current[useCase.id] = el
                              }}
                              src={useCase.image}
                              muted
                              loop
                              autoPlay
                              playsInline
                              preload="auto"
                              className="w-full h-full object-cover"
                              poster={(useCase as any).poster}
                            />
                          ) : (
                            <img
                              src={useCase.image}
                              alt={getImageAlt(useCase.id)}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div>

                        {/* Secondary media (smaller, overlapping) */}
                        {/* <div className="absolute -bottom-6 -left-6 w-2/3 h-40 rounded-xl overflow-hidden shadow-md border-2 border-white z-10">
                          {useCase.renderContent ? (
                            <div className="relative w-full h-full">
                              {useCase.renderContent}
                            </div>
                          ) : useCase.isVideo ? (
                            <video
                              src={useCase.image}
                              muted
                              loop
                              autoPlay
                              playsInline
                              preload="auto"
                              className="w-full h-full object-cover"
                              poster={(useCase as any).poster}
                            />
                          ) : (
                            <img
                              src={useCase.image}
                              alt={getImageAlt(useCase.id)}
                              className="w-full h-full object-cover"
                            />
                          )}
                        </div> */}

                        {/* Icon badge */}
                        <div className="absolute -top-3 -right-3 w-14 h-14 bg-white rounded-full shadow-lg flex items-center justify-center border border-gray-100">
                          <IconComponent
                            className={`w-7 h-7 bg-gradient-to-r ${useCase.color} bg-clip-text text-transparent`}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6 md:p-8 lg:p-10 md:w-3/6 lg:w-3/6 flex flex-col justify-center">
                    <h3 className="text-[28px] md:text-[32px] lg:text-[36px] font-bold text-gray-900 leading-tight mb-4">
                      {useCase.title}
                    </h3>
                    <p className="text-[16px] md:text-[17px] text-gray-600 leading-relaxed mb-6">
                      {useCase.description}
                    </p>

                    {/* Stats badge */}
                    <div className="inline-flex items-center gap-2 px-4 py-2 bg-gray-50 rounded-full text-[13px] md:text-[14px] font-medium mb-8 w-fit">
                      <Star className="w-4 h-4 text-yellow-500" />
                      <span className="text-gray-700">{useCase.stats}</span>
                    </div>

                    {/* Link-style actions */}
                    <div className="space-y-3">
                      <Link
                        href={link}
                        className="group hover:underline inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium text-[15px] md:text-[16px] transition-colors"
                      >
                        <span>{useCase.cta}</span>
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                      </Link>
                      {/* <a
                        href="#"
                        className="group inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 font-medium text-[15px] md:text-[16px] transition-colors"
                      >
                        <span>API documentation</span>
                        <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                      </a> */}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>

      {/* CSS Animations */}
      <style jsx>{`
        @keyframes dash {
          to {
            stroke-dashoffset: -30;
          }
        }

        @keyframes float {
          0%,
          100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @keyframes glow {
          0%,
          100% {
            box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
          }
          50% {
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.8),
              0 0 30px rgba(59, 130, 246, 0.6);
          }
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        @keyframes ripple {
          0% {
            transform: scale(0);
            opacity: 1;
          }
          100% {
            transform: scale(4);
            opacity: 0;
          }
        }

        @keyframes pulse-glow {
          0%,
          100% {
            opacity: 0.5;
          }
          50% {
            opacity: 0.8;
          }
        }
      `}</style>
    </section>
  )
}
