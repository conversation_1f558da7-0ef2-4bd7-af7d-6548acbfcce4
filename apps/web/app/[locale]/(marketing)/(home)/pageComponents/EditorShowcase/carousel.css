/* 无限滚动走马灯动画 */
@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 40s linear infinite;
  width: fit-content;
  will-change: transform; /* 优化性能 */
}

/* 悬停时暂停动画 */
.animate-scroll:hover {
  animation-play-state: paused;
}

/* 平滑的悬停过渡 */
.animate-scroll.hover\:pause:hover {
  animation-play-state: paused;
  transition: all 0.3s ease;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .animate-scroll {
    animation-duration: 20s; /* 移动端稍快一些 */
  }
}

@media (max-width: 480px) {
  .animate-scroll {
    animation-duration: 15s; /* 小屏幕更快 */
  }
}

/* 平滑的渐变遮罩效果 */
.carousel-gradient-left {
  background: linear-gradient(to right, white 0%, transparent 100%);
}

.carousel-gradient-right {
  background: linear-gradient(to left, white 0%, transparent 100%);
}

/* Logo 悬停效果增强 */
.logo-item {
  transition: all 0.3s ease;
  filter: grayscale(100%) opacity(0.6);
}

.logo-item:hover {
  filter: grayscale(0%) opacity(1);
  transform: scale(1.05);
}

/* 确保动画在不同浏览器中的兼容性 */
@-webkit-keyframes scroll {
  0% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}

@-moz-keyframes scroll {
  0% {
    -moz-transform: translateX(0);
    transform: translateX(0);
  }
  100% {
    -moz-transform: translateX(-50%);
    transform: translateX(-50%);
  }
}

/* 为不同速度提供额外的动画类 */
.animate-scroll-slow {
  animation: scroll 45s linear infinite;
}

.animate-scroll-fast {
  animation: scroll 15s linear infinite;
}

/* 反向滚动动画 */
@keyframes scroll-reverse {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-scroll-reverse {
  animation: scroll-reverse 30s linear infinite;
}
