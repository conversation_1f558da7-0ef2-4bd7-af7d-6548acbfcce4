import './scroll-animations.css'
import { HeroSection } from './pageComponents/HeroSection'
import { ProductAdvantages } from './pageComponents/ProductAdvantages'
import { UseCases } from './pageComponents/UseCases'
import { HowToGuide } from './pageComponents/HowToGuide'
import { BackgroundRemovalSection } from './pageComponents/BackgroundRemovalSection'
import { Testimonials } from './pageComponents/Testimonials'
import { FAQ } from './pageComponents/FAQ'
import { BackgroundEraseDemo } from './pageComponents/BackgroundEraseDemo'
import { ObjectRemovalDemo } from './pageComponents/ObjectRemovalDemo'
import { EditorShowcase } from './pageComponents/EditorShowcase'
import { getTranslations } from 'next-intl/server'
//
export async function generateMetadata() {
  const t = await getTranslations('home')
  return {
    title: t('title'),
    description: t('description'),
    keywords: t('keywords'),
  }
}

const link = '/playground'
const Index = async () => {
  const t = await getTranslations('home')

  return (
    <div className="w-full min-h-screen text-gray-900">
      {/* Hero Section */}
      <HeroSection />


      {/* Editor Showcase */}
      <EditorShowcase />

      {/* Product Advantages Section */}
      <ProductAdvantages link={link}/>

      {/* Demo Section with Background Erase and Object Removal */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              {t('demoSectionTitle')}
            </h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
              {t('demoSectionDescription')}
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            <BackgroundEraseDemo />
            <ObjectRemovalDemo />
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <UseCases link={link}/>

      {/* How To Guide Section */}
      <HowToGuide link={link}/>

      {/* Background Removal Section */}
      <BackgroundRemovalSection />

      {/* Testimonials Section */}
      <Testimonials />

      {/* FAQ Section */}
      <FAQ />
    </div>
  )
}

export default Index
