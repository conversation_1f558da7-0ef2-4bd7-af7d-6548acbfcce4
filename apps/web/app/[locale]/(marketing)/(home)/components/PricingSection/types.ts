import { LucideIcon } from 'lucide-react'

export interface PlanFeature {
  text: string
  included: boolean
}

export interface PlanConfig {
  id: string
  name: 'free' | 'basic' | 'advanced' | 'unlimited'
  title: string
  icon: LucideIcon
  price: number
  originalPrice?: number
  monthlyEquivalent?: number // For yearly plans, what would be the monthly price
  credits: number | 'unlimited'
  features: PlanFeature[]
  popular?: boolean
  disabled?: boolean
  buttonText: string
  buttonVariant?: 'default' | 'light'
  saveText?: string
  concurrentJobs?: number
  cloudStorageDays?: number
}

export interface BillingConfig {
  monthly: PlanConfig[]
  yearly: PlanConfig[]
}

export interface ThemeConfig {
  light: {
    background: string
    cardBackground: string
    textPrimary: string
    textSecondary: string
    accent: string
    border: string
    shadow: string
    toggleBackground: string
    toggleActive: string
  }
  dark: {
    background: string
    cardBackground: string
    textPrimary: string
    textSecondary: string
    accent: string
    border: string
    shadow: string
    toggleBackground: string
    toggleActive: string
  }
}

export interface PricingSectionProps {
  needTitle?: boolean
  className?: string
  theme?: 'light' | 'dark' | 'auto'
  defaultBillingCycle?: 'monthly' | 'yearly'
}