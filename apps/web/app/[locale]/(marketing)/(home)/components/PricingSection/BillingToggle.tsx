'use client'

import { ThemeConfig } from './types'

interface BillingToggleProps {
  billingCycle: 'monthly' | 'yearly'
  onChange: (cycle: 'monthly' | 'yearly') => void
  theme: ThemeConfig['light'] | ThemeConfig['dark']
  className?: string
}

export default function BillingToggle({
  billingCycle,
  onChange,
  theme,
  className = ''
}: BillingToggleProps) {
  return (
    <div className={`flex items-center justify-center mb-12 ${className}`}>
      {/* Monthly/Yearly Toggle */}
      <div className="flex items-center bg-gray-100 dark:bg-gray-700 rounded-full p-1">
        {/* Monthly Button */}
        <button
          className={`
            relative px-6 py-2 text-sm font-medium rounded-full transition-all duration-300 z-20
            ${billingCycle === 'monthly' 
              ? 'text-white bg-blue-500' 
              : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }
          `}
          onClick={() => onChange('monthly')}
        >
          Monthly
        </button>
        
        {/* Yearly Button */}
        <button
          className={`
            relative px-6 py-2 text-sm font-medium rounded-full transition-all duration-300 z-20 flex items-center gap-2
            ${billingCycle === 'yearly' 
              ? 'text-white bg-blue-500' 
              : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
            }
          `}
          onClick={() => onChange('yearly')}
        >
          <span>Yearly</span>
          <span className={`
            text-xs font-bold px-2 py-0.5 rounded-full flex items-center gap-1 transition-colors duration-300
            ${billingCycle === 'yearly'
              ? 'bg-white/20 text-white'
              : 'bg-gradient-to-r from-purple-500 to-blue-500 text-white'
            }
          `}>
            👑 Save 25%
          </span>
        </button>
      </div>
    </div>
  )
}