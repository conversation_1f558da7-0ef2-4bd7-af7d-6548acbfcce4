import { Music, Radio, Gem, Infinity } from 'lucide-react'
import { BillingConfig, ThemeConfig } from './types'

export const PRICING_CONFIG: BillingConfig = {
  monthly: [
    {
      id: 'basic-monthly',
      name: 'basic',
      title: 'Basic',
      icon: Radio,
      price: 8.9,
      credits: 100,
      buttonText: 'Subscribe Now',
      saveText: 'Save 10%',
      concurrentJobs: 1,
      cloudStorageDays: 30,
      features: [
        { text: '100 credits/month', included: true },
        { text: 'All AI features', included: true },
        { text: '30 days cloud storage', included: true },
        { text: 'Email support', included: true },
        { text: '1 concurrent job', included: true },
        { text: 'No watermark export', included: true },
        { text: 'Priority queue', included: false },
        { text: 'API access', included: false },
      ],
    },
    {
      id: 'advanced-monthly',
      name: 'advanced',
      title: 'Advanced',
      icon: Gem,
      price: 15.9,
      credits: 900,
      buttonText: 'Subscribe Now',
      buttonVariant: 'light',
      saveText: 'Save 20%',
      popular: true,
      concurrentJobs: 2,
      cloudStorageDays: 365,
      features: [
        { text: '900 credits/month', included: true },
        { text: 'All AI features', included: true },
        { text: '365 days cloud storage', included: true },
        { text: 'Priority support', included: true },
        { text: '2 concurrent jobs', included: true },
        { text: 'No watermark export', included: true },
        { text: 'Priority queue', included: true },
        { text: 'API access', included: true },
      ],
    },
    {
      id: 'unlimited-monthly',
      name: 'unlimited',
      title: 'Unlimited',
      icon: Infinity,
      price: 39.5,
      credits: 'unlimited',
      buttonText: 'Subscribe Now',
      saveText: 'Save 60%',
      concurrentJobs: 4,
      cloudStorageDays: 365,
      features: [
        { text: 'Unlimited credits', included: true },
        { text: 'All AI features', included: true },
        { text: '365 days cloud storage', included: true },
        { text: 'Dedicated support', included: true },
        { text: '4 concurrent jobs', included: true },
        { text: 'No watermark export', included: true },
        { text: 'Highest priority', included: true },
        { text: 'White-label solution', included: true },
      ],
    },
    {
      id: 'free-monthly',
      name: 'free',
      title: 'Free',
      icon: Music,
      price: 0,
      credits: 5,
      buttonText: '',
      disabled: true,
      cloudStorageDays: 1,
      features: [
        { text: '5 free credits', included: true },
        { text: '1 day cloud storage', included: true },
        { text: 'Basic AI features', included: true },
        { text: 'Advanced AI features', included: false },
        { text: 'Priority queue', included: false },
        { text: 'Batch processing', included: false },
      ],
    },
  ],

  yearly: [
    {
      id: 'basic-yearly',
      name: 'basic',
      title: 'Basic',
      icon: Radio,
      price: 87.6,
      monthlyEquivalent: 8.9,
      credits: 1300,
      buttonText: 'Subscribe Now',
      concurrentJobs: 1,
      cloudStorageDays: 30,
      features: [
        { text: '1,300 credits/year ', included: true },
        { text: 'All AI features', included: true },
        { text: '30 days cloud storage', included: true },
        { text: 'Email support', included: true },
        { text: '1 concurrent job', included: true },
        { text: 'No watermark export', included: true },
        { text: 'Yearly exclusive features', included: true },
        { text: 'Priority queue', included: false },
      ],
    },
    {
      id: 'advanced-yearly',
      name: 'advanced',
      title: 'Advanced',
      icon: Gem,
      price: 143.2,
      monthlyEquivalent: 15.9,
      credits: 12000,
      buttonText: 'Subscribe Now',
      buttonVariant: 'light',
      popular: true,
      concurrentJobs: 2,
      cloudStorageDays: 365,
      features: [
        { text: '12,000 credits/year', included: true },
        { text: 'All AI features', included: true },
        { text: '365 days cloud storage', included: true },
        { text: 'Priority support', included: true },
        { text: '2 concurrent jobs', included: true },
        { text: 'No watermark export', included: true },
        { text: 'Priority queue', included: true },
        { text: 'API access + yearly perks', included: true },
      ],
    },
    {
      id: 'unlimited-yearly',
      name: 'unlimited',
      title: 'Unlimited',
      icon: Infinity,
      price: 379.2,
      monthlyEquivalent: 39.5,
      credits: 'unlimited',
      buttonText: 'Subscribe Now',
      concurrentJobs: 6,
      cloudStorageDays: 365,
      features: [
        { text: 'Unlimited credits', included: true },
        { text: 'All AI features', included: true },
        { text: '365 days cloud storage', included: true },
        { text: 'Dedicated support', included: true },
        { text: '6 concurrent jobs ', included: true },
        { text: 'No watermark export', included: true },
        { text: 'Highest priority', included: true },
        { text: 'White-label + yearly exclusives', included: true },
      ],
    },
    {
      id: 'free-yearly',
      name: 'free',
      title: 'Free',
      icon: Music,
      price: 0,
      credits: 5,
      buttonText: '',
      disabled: true,
      cloudStorageDays: 1,
      features: [
        { text: '5 free credits', included: true },
        { text: '1 day cloud storage', included: true },
        { text: 'Basic AI features', included: true },
        { text: 'Advanced AI features', included: false },
        { text: 'Priority queue', included: false },
        { text: 'Batch processing', included: false },
      ],
    },
  ],
}

export const PRICING_THEME: ThemeConfig = {
  light: {
    background: 'bg-gray-50',
    cardBackground: 'bg-white',
    textPrimary: 'text-gray-900',
    textSecondary: 'text-gray-600',
    accent: 'bg-gradient-to-r from-[#4B6BFB] to-[#2F4BFB]',
    border: 'border-gray-200',
    shadow: 'shadow-lg hover:shadow-xl hover:shadow-[#4B6BFB]/10',
    toggleBackground: 'bg-gray-100',
    toggleActive: 'bg-[#4B6BFB]',
  },
  dark: {
    background: 'bg-gradient-to-br from-gray-900 via-purple-900/20 to-gray-900',
    cardBackground: 'bg-gray-800/80 backdrop-blur-sm',
    textPrimary: 'text-white',
    textSecondary: 'text-gray-300',
    accent: 'bg-gradient-to-r from-purple-500 to-pink-500',
    border: 'border-gray-700/50',
    shadow: 'shadow-lg hover:shadow-xl hover:shadow-purple-500/25',
    toggleBackground: 'bg-gray-700/50',
    toggleActive: 'bg-gradient-to-r from-purple-500 to-pink-500',
  },
}
