'use client'

import { Check, X } from 'lucide-react'
import { PlanConfig, ThemeConfig } from './types'

interface PricingCardProps extends PlanConfig {
  theme: ThemeConfig['light'] | ThemeConfig['dark']
  billingCycle: 'monthly' | 'yearly'
  className?: string
}

export default function PricingCard({
  title,
  icon: Icon,
  price,
  originalPrice,
  monthlyEquivalent,
  credits,
  features,
  popular = false,
  disabled = false,
  buttonText,
  buttonVariant = 'default',
  saveText,
  theme,
  billingCycle,
  className = '',
}: PricingCardProps) {
  const isYearly = billingCycle === 'yearly'

  // Calculate actual discount for yearly plans
  const actualDiscount =
    isYearly && monthlyEquivalent && price > 0
      ? Math.round(
          ((monthlyEquivalent * 12 - price) / (monthlyEquivalent * 12)) * 100
        )
      : 0

  return (
    <div
      className={`
        relative rounded-3xl p-6 lg:p-8
        ${
          popular
            ? 'bg-gradient-to-br from-purple-900/50 via-purple-800/30 to-pink-900/50 border-2 border-purple-500/50'
            : `${theme.cardBackground} ${theme.border} border-2`
        }
        ${theme.shadow}
        ${disabled ? 'opacity-60' : ''}
        ${className}
      `}
    >
      {/* Popular Badge */}
      {popular && (
        <div className="absolute -top-3 right-4">
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 rounded-full text-xs font-bold">
            Popular
          </div>
        </div>
      )}

      {/* Save Badge */}
      {actualDiscount > 0 && isYearly && !disabled && (
        <div className="absolute -top-2 -right-2">
          <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-bold">
            Save {actualDiscount}%
          </div>
        </div>
      )}

      {/* Header */}
      <div className="text-center mb-2">
        <h3 className={`text-2xl font-bold ${theme.textPrimary} mb-2`}>
          {title}
        </h3>

        {/* Price */}
        <div className="mb-4">
          {originalPrice && isYearly ? (
            <div className="flex items-center justify-center gap-2 mb-1">
              <span className={`text-lg line-through ${theme.textSecondary}`}>
                ${originalPrice}
              </span>
              <span className="text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full font-semibold">
                -{Math.round(((originalPrice - price) / originalPrice) * 100)}%
              </span>
            </div>
          ) : null}

          <div className="flex items-baseline justify-center">
            <span className={`text-3xl font-bold ${theme.textPrimary}`}>
              ${isYearly && price > 0 ? (price / 12).toFixed(1) : price}
            </span>
            <span className={`text-lg ${theme.textSecondary} ml-1`}>
              /month
            </span>
          </div>

          {/* Show yearly total for yearly plans */}
          {/* {isYearly && price > 0 && (
            <div className="flex items-center justify-center mt-2">
              <span className={`text-sm ${theme.textSecondary}`}>
                ${price} billed yearly. Cancel anytime.
              </span>
            </div>
          )} */}
        </div>

        {/* Credits */}
        <div className={`text-lg font-semibold ${theme.textSecondary}`}>
          {typeof credits === 'number'
            ? `${credits.toLocaleString()} credits`
            : 'Unlimited credits'}
        </div>
      </div>

      {/* Features */}
      <div className="space-y-4 mb-8">
        {features.map((feature, index) => (
          <div key={index} className="flex items-start gap-3">
            <div
              className={`
              flex-shrink-0 w-5 h-5 rounded-full flex items-center justify-center mt-0.5
              ${
                feature.included
                  ? 'bg-green-100 text-green-600'
                  : 'bg-gray-100 text-gray-400'
              }
            `}
            >
              {feature.included ? (
                <Check className="w-3 h-3" />
              ) : (
                <X className="w-3 h-3" />
              )}
            </div>
            <span
              className={`
              text-sm
              ${feature.included ? theme.textPrimary : theme.textSecondary}
            `}
            >
              {feature.text}
            </span>
          </div>
        ))}
      </div>

      {/* Button */}
      {buttonText && (
        <button
          disabled={disabled}
          className={`
            w-full py-4 px-6 rounded-xl font-semibold text-sm transition-all duration-300
            ${
              disabled
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : popular
                ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 transform hover:scale-105'
                : buttonVariant === 'light'
                ? `bg-white ${theme.textPrimary} border-2 ${theme.border} hover:bg-gray-50`
                : `${theme.accent} text-white hover:opacity-90 transform hover:scale-105`
            }
          `}
        >
          {buttonText}
        </button>
      )}
    </div>
  )
}
