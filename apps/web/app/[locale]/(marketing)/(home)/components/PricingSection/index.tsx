'use client'
import { useState, useEffect } from 'react'
import PricingCardBeta from '@shared/components/PricingCard-beta'
import { Music, Radio, Gem, Infinity, Activity } from 'lucide-react'
import { useLocale, useTranslations } from 'next-intl'
import { getUserFromClientCookies } from '@/utils/client-cookies'
//
interface Props {
  needTitle?: boolean
  className?: string
}
//

const PricingSection: React.FC<Props> = ({
  needTitle = true,
  className = 'leading-none',
}) => {
  const t = useTranslations()
  const user = getUserFromClientCookies()
  const whiteList = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
  ]

  const locale = useLocale()
  // 默认选择年度订阅
  const [isYearly, setIsYearly] = useState(true)
  // 检测是否为移动设备
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // 客户端检测移动设备
    const checkMobile = () => {
      const userAgent =
        navigator.userAgent || navigator.vendor || (window as any).opera
      return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
        userAgent.toLowerCase()
      )
    }

    setIsMobile(checkMobile())

    // 响应窗口大小变化
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const variables = {
    discount10: 10,
    discount20: 25, // 中档最高折扣
    discount60: 20,
    freeCredits: 5,
    cloudStorageDays: 1,

    // 折扣率
    discountBasic: 18,
    discountAdvanced: 25,
    discountUnlimited: 20,

    // 月度价格
    monthlyPriceBasic: 8.9,
    originalMonthlyPriceBasic: 14.9, // 新增原价
    creditsBasicMonthly: 100,
    concurrentJobsBasic: 1,
    cloudStorageDaysBasic: 30,

    monthlyPriceAdvanced: 15.9,
    originalMonthlyPriceAdvanced: 36.5, // 新增原价
    creditsAdvancedMonthly: 900,
    concurrentJobsAdvanced: 2,
    cloudStorageDaysAdvanced: 365,

    monthlyPriceUnlimited: 39.5,
    originalMonthlyPriceUnlimited: 95.9, // 新增原价
    concurrentJobsUnlimited: 4,

    // 年度价格 (已应用折扣)
    yearlyPriceBasic: 87.6, // 18% 折扣
    originalYearlyPriceBasic: 106.8, // 新增原价 (14.9 × 12 × (1 - 折扣))
    yearlyMonthlyBasic: 7.3, // 87.6 ÷ 12 = 每月平均价格
    originalYearlyMonthlyBasic: 14.9, // 新增原价 (原月价)
    yearSavingsBasic: 19.2, // (8.9 × 12) - 87.6 = 每年节省金额
    creditsBasicYearly: 1200, // 年度积分

    yearlyPriceAdvanced: 143.1, // 25% 折扣
    originalYearlyPriceAdvanced: 190.8, // 新增原价 (26.5 × 12 × (1 - 折扣))
    yearlyMonthlyAdvanced: 11.93, // 143.1 ÷ 12 = 每月平均价格
    originalYearlyMonthlyAdvanced: 26.5, // 新增原价 (原月价)
    yearSavingsAdvanced: 47.7, // (15.9 × 12) - 143.1 = 每年节省金额
    creditsAdvancedYearly: 10800, // 年度积分

    yearlyPriceUnlimited: 379.2, // 20% 折扣
    originalYearlyPriceUnlimited: 474.0, // 新增原价 (65.9 × 12 × (1 - 折扣))
    yearlyMonthlyUnlimited: 31.6, // 379.2 ÷ 12 = 每月平均价格
    originalYearlyMonthlyUnlimited: 65.9, // 新增原价 (原月价)
    yearSavingsUnlimited: 94.8, // (39.5 × 12) - 379.2 = 每年节省金额
  }

  const getPlans = () => {
    const allPlans = [
      {
        level: 1,
        title: t('price.basicPlan.title'),
        IconComponent: Radio,
        price: isYearly
          ? t('price.basicPlan.yearlyPricePerMonth', {
              yearlyMonthlyBasic: variables.yearlyMonthlyBasic,
              yearlyPriceBasic: variables.yearlyPriceBasic,
            })
          : t('price.basicPlan.price', {
              monthlyPriceBasic: variables.monthlyPriceBasic,
            }),
        originalPrice: isYearly
          ? t('price.basicPlan.yearlyPricePerMonth', {
              yearlyMonthlyBasic: variables.originalYearlyMonthlyBasic,
              yearlyPriceBasic: variables.originalYearlyPriceBasic,
            })
          : t('price.basicPlan.price', {
              monthlyPriceBasic: variables.originalMonthlyPriceBasic,
            }),
        discountPercent: isYearly
          ? Math.round(
              (1 -
                variables.yearlyMonthlyBasic /
                  variables.originalMonthlyPriceBasic) *
                100
            )
          : Math.round(
              (1 -
                variables.monthlyPriceBasic /
                  variables.originalMonthlyPriceBasic) *
                100
            ),
        credits: isYearly
          ? t('price.basicPlan.yearlyCredits', {
              creditsBasicYearly: variables.creditsBasicYearly,
            })
          : t('price.basicPlan.credits', {
              creditsBasic: variables.creditsBasicMonthly,
            }),
        saveText: isYearly
          ? t('price.yearSavings', {
              yearSavings: variables.yearSavingsBasic,
            })
          : t('price.save10', { discount10: variables.discount10 }),
        buttonText: t('price.subscribeButton'),
        billingPeriod: isYearly ? 'yearly' : 'monthly',
        billingLabel: isYearly
          ? t('price.yearlyBillingNote', {
              yearlyPrice: variables.yearlyPriceBasic,
            })
          : t('price.monthlyBillingNote'),
        features: [
          isYearly
            ? t('price.basicPlan.features.yearlyFeature1', {
                creditsBasicYearly: variables.creditsBasicYearly,
              })
            : t('price.basicPlan.features.feature1', {
                creditsBasic: variables.creditsBasicMonthly,
              }),
          'No watermark export',
          t('price.basicPlan.features.feature2'),
          t('price.basicPlan.features.feature3'),
          t('price.basicPlan.features.feature4'),
          t('price.basicPlan.features.feature5', {
            concurrentJobsBasic: variables.concurrentJobsBasic,
          }),
          t('price.basicPlan.features.feature6'),
          t('price.basicPlan.features.feature7'),
          t('price.basicPlan.features.feature8', {
            cloudStorageDaysBasic: variables.cloudStorageDaysBasic,
          }),
        ],
      },
      {
        level: 2,
        title: t('price.advancedPlan.title'),
        IconComponent: Gem,
        price: isYearly
          ? t('price.advancedPlan.yearlyPricePerMonth', {
              yearlyMonthlyAdvanced: variables.yearlyMonthlyAdvanced,
              yearlyPriceAdvanced: variables.yearlyPriceAdvanced,
            })
          : t('price.advancedPlan.price', {
              monthlyPriceAdvanced: variables.monthlyPriceAdvanced,
            }),
        originalPrice: isYearly
          ? t('price.advancedPlan.yearlyPricePerMonth', {
              yearlyMonthlyAdvanced: variables.originalYearlyMonthlyAdvanced,
              yearlyPriceAdvanced: variables.originalYearlyPriceAdvanced,
            })
          : t('price.advancedPlan.price', {
              monthlyPriceAdvanced: variables.originalMonthlyPriceAdvanced,
            }),
        discountPercent: isYearly
          ? Math.round(
              (1 -
                variables.yearlyMonthlyAdvanced /
                  variables.originalMonthlyPriceAdvanced) *
                100
            )
          : Math.round(
              (1 -
                variables.monthlyPriceAdvanced /
                  variables.originalMonthlyPriceAdvanced) *
                100
            ),
        credits: isYearly
          ? t('price.advancedPlan.yearlyCredits', {
              creditsAdvancedYearly: variables.creditsAdvancedYearly,
            })
          : t('price.advancedPlan.credits', {
              creditsAdvanced: variables.creditsAdvancedMonthly,
            }),
        saveText: isYearly
          ? t('price.yearSavings', {
              yearSavings: variables.yearSavingsAdvanced,
            })
          : t('price.save20', { discount20: variables.discount20 }),
        isPopular: true,
        buttonText: t('price.subscribeButton'),
        buttonVariant: 'light',
        popularBadge: t('price.popularBadge'),
        billingPeriod: isYearly ? 'yearly' : 'monthly',
        billingLabel: isYearly
          ? t('price.yearlyBillingNote', {
              yearlyPrice: variables.yearlyPriceAdvanced,
            })
          : t('price.monthlyBillingNote'),
        features: [
          isYearly
            ? t('price.advancedPlan.features.yearlyFeature1', {
                creditsAdvancedYearly: variables.creditsAdvancedYearly,
              })
            : t('price.advancedPlan.features.feature1', {
                creditsAdvanced: variables.creditsAdvancedMonthly,
              }),
          'No watermark export',
          t('price.advancedPlan.features.feature2'),
          t('price.advancedPlan.features.feature3'),
          t('price.advancedPlan.features.feature4'),
          t('price.advancedPlan.features.feature5', {
            concurrentJobsAdvanced: variables.concurrentJobsAdvanced,
          }),
          t('price.advancedPlan.features.feature6'),
          t('price.advancedPlan.features.feature7'),
          t('price.advancedPlan.features.feature8', {
            cloudStorageDaysAdvanced: variables.cloudStorageDaysAdvanced,
          }),
        ],
      },
      {
        level: 3,
        title: t('price.unlimitedPlan.title'),
        IconComponent: Infinity,
        price: isYearly
          ? t('price.unlimitedPlan.yearlyPricePerMonth', {
              yearlyMonthlyUnlimited: variables.yearlyMonthlyUnlimited,
              yearlyPriceUnlimited: variables.yearlyPriceUnlimited,
            })
          : t('price.unlimitedPlan.price', {
              monthlyPriceUnlimited: variables.monthlyPriceUnlimited,
            }),
        originalPrice: isYearly
          ? t('price.unlimitedPlan.yearlyPricePerMonth', {
              yearlyMonthlyUnlimited: variables.originalYearlyMonthlyUnlimited,
              yearlyPriceUnlimited: variables.originalYearlyPriceUnlimited,
            })
          : t('price.unlimitedPlan.price', {
              monthlyPriceUnlimited: variables.originalMonthlyPriceUnlimited,
            }),
        discountPercent: isYearly
          ? Math.round(
              (1 -
                variables.yearlyMonthlyUnlimited /
                  variables.originalMonthlyPriceUnlimited) *
                100
            )
          : Math.round(
              (1 -
                variables.monthlyPriceUnlimited /
                  variables.originalMonthlyPriceUnlimited) *
                100
            ),
        credits: t('price.unlimitedPlan.credits'),
        saveText: isYearly
          ? t('price.yearSavings', {
              yearSavings: variables.yearSavingsUnlimited,
            })
          : t('price.save60', { discount60: variables.discount60 }),
        buttonText: t('price.subscribeButton'),
        billingPeriod: isYearly ? 'yearly' : 'monthly',
        billingLabel: isYearly
          ? t('price.yearlyBillingNote', {
              yearlyPrice: variables.yearlyPriceUnlimited,
            })
          : t('price.monthlyBillingNote'),
        features: [
          'No watermark export',
          t('price.unlimitedPlan.features.feature1'),
          t('price.unlimitedPlan.features.feature2'),
          t('price.unlimitedPlan.features.feature3'),
          t('price.unlimitedPlan.features.feature4'),
          t('price.unlimitedPlan.features.feature5', {
            concurrentJobsUnlimited: variables.concurrentJobsUnlimited,
          }),
          t('price.unlimitedPlan.features.feature6'),
          t('price.unlimitedPlan.features.feature7'),
          t('price.unlimitedPlan.features.feature8'),
        ],
      },
      {
        level: 0,
        title: t('price.freePlan.title'),
        IconComponent: Activity,
        price: t('price.freePlan.price'),
        credits: t('price.freePlan.credits', {
          freeCredits: variables.freeCredits,
        }),
        buttonText: t('price.tryNowButton'),
        features: [
          t('price.freePlan.features.feature1', {
            freeCredits: variables.freeCredits,
          }),
          'Download with watermark',
          t('price.freePlan.features.feature2', {
            cloudStorageDays: variables.cloudStorageDays,
          }),
          t('price.freePlan.features.feature3'),
        ],
        disabledFeatures: [
          t('price.freePlan.disabledFeatures.feature1'),
          t('price.freePlan.disabledFeatures.feature2'),
          t('price.freePlan.disabledFeatures.feature3'),
        ],
        disabled: true,
      },
    ] as PlanFeatures[]

    return allPlans
  }

  return (
    <section
      className={`max-w-7xl mx-auto px-4 overflow-auto min-h-[800px] ${
        needTitle ? '' : '!py-2'
      }`}
    >
      <div className="text-center mb-8">
        {/* 订阅类型切换按钮 */}
        <div className="flex justify-center items-center mb-8">
          <div className="bg-gray-800 p-1 rounded-lg inline-flex">
            <button
              onClick={() => setIsYearly(false)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                !isYearly
                  ? 'bg-gray-700 text-white shadow-sm'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              {t('price.monthlyBilling')}
            </button>
            <button
              onClick={() => setIsYearly(true)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 flex items-center ${
                isYearly
                  ? 'bg-gray-700 text-white shadow-sm'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              {t('price.yearlyBilling')}
              <span className="ml-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs px-2 py-0.5 rounded-full">
                {t('price.saveBig')}
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* 根据设备类型使用不同的布局 */}
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6`}>
        {getPlans().map((plan, index) => (
          <div key={index} className={className}>
            {/* <PricingCard className={className} {...plan} isYearly={isYearly} /> */}
            <PricingCardBeta
              className={className}
              {...plan}
              isYearly={isYearly}
            />
          </div>
        ))}
      </div>
    </section>
  )
}

export default PricingSection
