import Image from 'next/image'
import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'

export default async function FeatureShowcase() {
  const t = await getTranslations()
  const features = [
    {
      title: t('FeatureShowcase.features.0.title'),
      desc: t('FeatureShowcase.features.0.desc'),
      btn: t('FeatureShowcase.features.0.btn'),
      img: '/gif/iShot_2025-07-04_21.53.16.gif',
      href: '/ai/background-removal',
    },
    {
      title: t('FeatureShowcase.features.1.title'),
      desc: t('FeatureShowcase.features.1.desc'),
      btn: t('FeatureShowcase.features.1.btn'),
      img: '/videos/image-removeobj.mp4',
      isVideo: true,
      // todo @lilsnake: Remove Object from Image incoming -> 修改url
      href: '/ai/background-removal',
    },
    {
      title: t('FeatureShowcase.features.2.title'),
      desc: t('FeatureShowcase.features.2.desc'),
      btn: t('FeatureShowcase.features.2.btn'),
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/iShot_2025-07-07_00.15.49.gif',
      href: '/ai/ghibli',
    },
    {
      title: t('FeatureShowcase.features.3.title'),
      desc: t('FeatureShowcase.features.3.desc'),
      btn: t('FeatureShowcase.features.3.btn'),
      img: '/gif/iShot_2025-07-04_21.58.26.gif',
      href: '/ai/photo-restoration',
    },
    {
      title: t('FeatureShowcase.features.4.title'),
      desc: t('FeatureShowcase.features.4.desc'),
      btn: t('FeatureShowcase.features.4.btn'),
      img: '/gif/iShot_2025-07-04_22.03.27.gif',
      href: '/ai/face-swap',
    },
    {
      title: t('FeatureShowcase.features.5.title'),
      desc: t('FeatureShowcase.features.5.desc'),
      btn: t('FeatureShowcase.features.5.btn'),
      img: '/gif/1714029566071.gif',
      // todo @lilsnake: Image Extraction Tool incoming -> 修改url
      href: '/ai/background-removal',
    },
  ]
  return (
    <section className="w-full bg-[#141318] py-16" id="feature-showcase">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl md:text-4xl font-bold text-center text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-4">
          {t('FeatureShowcase.title')}
        </h2>
        <p className="text-lg md:text-xl text-center text-gray-300 mb-12 max-w-4xl mx-auto">
          {t('FeatureShowcase.description')}
        </p>
        <div className="flex flex-col gap-8 md:gap-6">
          {features.map((f, i) => {
            const isEven = i % 2 === 0
            return (
              <div
                key={i}
                className={`flex flex-col md:flex-row items-center justify-between rounded-2xl px-0 py-0 md:py-14 md:px-0 gap-8 md:gap-6 w-full ${
                  isEven ? 'md:flex-row' : 'md:flex-row-reverse'
                }`}
              >
                {/* 图片部分 */}
                <div className="w-full md:w-1/2 flex justify-center md:justify-start mb-4 md:mb-0">
                  {f.isVideo ? (
                    <>
                      <video
                        className="max-w-full h-auto rounded-xl object-cover"
                        src={f.img}
                        muted
                        playsInline
                        autoPlay
                        loop
                        width={500}
                        height={220}
                      />
                      <img
                        src={features[0]?.img || ''}
                        alt={f.title}
                        width={500}
                        height={220}
                        loading="lazy"
                        className="sr-only"
                      />
                    </>
                  ) : (
                    <Image
                      src={f.img}
                      alt={f.title}
                      width={500}
                      height={220}
                      loading="lazy"
                      className="rounded-xl object-cover max-w-full h-auto"
                      unoptimized
                    />
                  )}
                </div>
                {/* 文字部分 */}
                <div className="w-full md:w-1/2 flex flex-col justify-center items-center md:items-start text-center md:text-left px-2 md:px-0">
                  <h3 className="text-2xl md:text-3xl font-bold text-indigo-100 mb-4">
                    {f.title}
                  </h3>
                  <p className="text-gray-300 mb-6 text-base md:text-lg max-w-xl">
                    {f.desc}
                  </p>
                  <span className="inline-block rounded-full p-[1px] bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                    <Link href={f.href}>
                      <button
                        className="relative rounded-full px-7 py-2 font-semibold text-base md:text-lg text-white bg-[#181028] hover:bg-[#2d1d34] transition-all"
                        style={{ outline: 'none', border: 'none' }}
                      >
                        {f.btn}
                      </button>
                    </Link>
                  </span>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}
