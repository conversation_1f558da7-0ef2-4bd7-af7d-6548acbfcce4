'use client'

import { <PERSON> } from '@i18n/routing'
import { useTranslations } from 'next-intl'

const HeroButtons = () => {
  const t = useTranslations()

  return (
    <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto mt-6 sm:mt-8 md:mt-10">
      <Link
        href="/tools/photo-to-anime"
        className="bg-gradient-to-r from-purple-500 to-pink-500 hover:opacity-90 transition-all text-white px-6 sm:px-8 py-3 rounded-lg shadow-lg hover:scale-105 font-semibold text-base sm:text-lg drop-shadow-[0_2px_16px_rgba(236,72,255,0.5)] w-full sm:w-auto text-center"
      >
        {t('home.cta')}
      </Link>
      <Link
        href="/templates"
        className="bg-transparent border-2 border-purple-400/60 text-purple-300 hover:text-white hover:border-purple-300 hover:bg-purple-500/20 px-6 sm:px-8 py-3 rounded-lg shadow-lg hover:scale-105 transition-all font-semibold text-base sm:text-lg backdrop-blur-sm w-full sm:w-auto text-center"
      >
        {t('home.exploreMoreTools')}
      </Link>
    </div>
  )
}

export default HeroButtons
