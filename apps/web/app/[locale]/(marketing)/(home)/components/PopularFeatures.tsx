'use client'

import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import ScrollAnimation from './ScrollAnimation/ScrollAnimation'
import {
  Camera,
  Image as ImageIcon,
  FileText,
  Zap,
  Shirt,
  Sparkles,
  RefreshCw,
  Type,
  Heart,
  Users,
} from 'lucide-react'

const PopularFeatures = () => {
  const t = useTranslations()

  // Define the popular features data with appropriate icons
  const popularFeatures = [
    {
      title: t('tools.business.items.aiClothesChanger.title'),
      href: '/tools/ai-clothes-changer',
      icon: <Shirt className="w-4 h-4 text-white" />,
      bgClass: 'from-purple-600 via-purple-700 to-indigo-800',
      hoverBgClass:
        'hover:from-purple-500 hover:via-purple-600 hover:to-indigo-700',
      borderClass: 'border-purple-400/30',
      shadowClass:
        'shadow-lg shadow-purple-500/25 hover:shadow-xl hover:shadow-purple-400/40',
      iconBgClass: 'bg-gradient-to-br from-purple-400 to-indigo-500',
      textClass: 'text-white font-semibold',
    },
    {
      title: t('tools.creative.items.ghibli.title'),
      href: '/tools/ghibli',
      icon: <Sparkles className="w-4 h-4 text-white" />,
      bgClass: 'from-fuchsia-600 via-pink-600 to-rose-700',
      hoverBgClass:
        'hover:from-fuchsia-500 hover:via-pink-500 hover:to-rose-600',
      borderClass: 'border-fuchsia-400/30',
      shadowClass:
        'shadow-lg shadow-fuchsia-500/25 hover:shadow-xl hover:shadow-fuchsia-400/40',
      iconBgClass: 'bg-gradient-to-br from-fuchsia-400 to-pink-500',
      textClass: 'text-white font-semibold',
    },
    {
      title: t('tools.memory.items.photoRestore.title'),
      href: '/tools/photo-restore',
      icon: <RefreshCw className="w-4 h-4 text-white" />,
      bgClass: 'from-emerald-600 via-teal-600 to-cyan-700',
      hoverBgClass:
        'hover:from-emerald-500 hover:via-teal-500 hover:to-cyan-600',
      borderClass: 'border-emerald-400/30',
      shadowClass:
        'shadow-lg shadow-emerald-500/25 hover:shadow-xl hover:shadow-emerald-400/40',
      iconBgClass: 'bg-gradient-to-br from-emerald-400 to-teal-500',
      textClass: 'text-white font-semibold',
    },
    {
      title: t('tools.imageTools.items.textToImage.title'),
      href: '/tools/ai-text-to-image',
      icon: <Type className="w-4 h-4 text-white" />,
      bgClass: 'from-blue-600 via-indigo-600 to-purple-700',
      hoverBgClass:
        'hover:from-blue-500 hover:via-indigo-500 hover:to-purple-600',
      borderClass: 'border-blue-400/30',
      shadowClass:
        'shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-400/40',
      iconBgClass: 'bg-gradient-to-br from-blue-400 to-indigo-500',
      textClass: 'text-white font-semibold',
    },
    {
      title: t('tools.fun.items.aiHug.title'),
      href: '/tools/ai-hug',
      icon: <Heart className="w-4 h-4 text-white" />,
      bgClass: 'from-orange-600 via-red-600 to-pink-700',
      hoverBgClass: 'hover:from-orange-500 hover:via-red-500 hover:to-pink-600',
      borderClass: 'border-orange-400/30',
      shadowClass:
        'shadow-lg shadow-orange-500/25 hover:shadow-xl hover:shadow-orange-400/40',
      iconBgClass: 'bg-gradient-to-br from-orange-400 to-red-500',
      textClass: 'text-white font-semibold',
    },
    {
      title: t('tools.fun.items.aiFaceSwap.title'),
      href: '/tools/ai-face-swap',
      icon: <Users className="w-4 h-4 text-white" />,
      bgClass: 'from-violet-600 via-purple-600 to-fuchsia-700',
      hoverBgClass:
        'hover:from-violet-500 hover:via-purple-500 hover:to-fuchsia-600',
      borderClass: 'border-violet-400/30',
      shadowClass:
        'shadow-lg shadow-violet-500/25 hover:shadow-xl hover:shadow-violet-400/40',
      iconBgClass: 'bg-gradient-to-br from-violet-400 to-purple-500',
      textClass: 'text-white font-semibold',
    },
  ]

  return (
    <ScrollAnimation direction="up" delay={800} duration={1000}>
      <div className="text-left mb-6">
        <h2 className="text-xl md:text-2xl font-bold text-indigo-100 mb-4">
          {t('home.discoverPopularFeatures')}
        </h2>
      </div>

      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
        {popularFeatures.map((feature, index) => (
          <Link
            key={index}
            href={feature.href}
            className={`group bg-white/5 hover:bg-gradient-to-r ${feature.bgClass} backdrop-blur-sm border border-white/10 hover:border-white/30 rounded-xl px-3 py-3 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg`}
          >
            <div className="flex flex-col items-center gap-2 text-center">
              <div
                className={`w-8 h-8 ${feature.iconBgClass} rounded-lg flex items-center justify-center flex-shrink-0 transition-all duration-300`}
              >
                {feature.icon}
              </div>
              <h3 className="text-xs font-medium text-gray-300 group-hover:text-white transition-colors leading-tight">
                {feature.title}
              </h3>
            </div>
          </Link>
        ))}
      </div>
    </ScrollAnimation>
  )
}

export default PopularFeatures
