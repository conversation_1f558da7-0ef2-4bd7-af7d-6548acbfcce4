'use client'

import React, { useEffect, useRef, useState } from 'react'

interface AdvancedScrollAnimationProps {
  children: React.ReactNode
  className?: string
  threshold?: number
  delay?: number
  duration?: number
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade' | 'scale' | 'rotate'
  distance?: number
  parallax?: boolean
  parallaxSpeed?: number
  stagger?: boolean
  staggerDelay?: number
  ease?: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'cubic-bezier'
  customEase?: string
}

const AdvancedScrollAnimation: React.FC<AdvancedScrollAnimationProps> = ({
  children,
  className = '',
  threshold = 0.1,
  delay = 0,
  duration = 800,
  direction = 'up',
  distance = 50,
  parallax = false,
  parallaxSpeed = 0.5,
  stagger = false,
  staggerDelay = 100,
  ease = 'cubic-bezier',
  customEase = '0.25, 0.46, 0.45, 0.94',
}) => {
  const [isVisible, setIsVisible] = useState(false)
  const [hasAnimated, setHasAnimated] = useState(false)
  const [scrollY, setScrollY] = useState(0)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true)
          setHasAnimated(true)
        }
      },
      {
        threshold,
        rootMargin: '0px 0px -50px 0px',
      }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [threshold, hasAnimated])

  useEffect(() => {
    if (!parallax) return

    const handleScroll = () => {
      setScrollY(window.scrollY)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [parallax])

  const getEaseFunction = () => {
    switch (ease) {
      case 'ease':
        return 'ease'
      case 'ease-in':
        return 'ease-in'
      case 'ease-out':
        return 'ease-out'
      case 'ease-in-out':
        return 'ease-in-out'
      case 'cubic-bezier':
        return `cubic-bezier(${customEase})`
      default:
        return `cubic-bezier(${customEase})`
    }
  }

  const getInitialTransform = () => {
    let transform = ''

    switch (direction) {
      case 'up':
        transform += `translateY(${distance}px)`
        break
      case 'down':
        transform += `translateY(-${distance}px)`
        break
      case 'left':
        transform += `translateX(${distance}px)`
        break
      case 'right':
        transform += `translateX(-${distance}px)`
        break
      case 'scale':
        transform += 'scale(0.8)'
        break
      case 'rotate':
        transform += 'rotate(5deg)'
        break
      case 'fade':
        transform += 'translateY(0px)'
        break
    }

    if (parallax && ref.current) {
      const rect = ref.current.getBoundingClientRect()
      const parallaxOffset = (scrollY - rect.top) * parallaxSpeed
      transform += ` translateY(${parallaxOffset}px)`
    }

    return transform
  }

  const getFinalTransform = () => {
    let transform = ''

    switch (direction) {
      case 'up':
      case 'down':
      case 'left':
      case 'right':
        transform = 'translateY(0px) translateX(0px)'
        break
      case 'scale':
        transform = 'scale(1)'
        break
      case 'rotate':
        transform = 'rotate(0deg)'
        break
      case 'fade':
        transform = 'translateY(0px)'
        break
    }

    if (parallax && ref.current) {
      const rect = ref.current.getBoundingClientRect()
      const parallaxOffset = (scrollY - rect.top) * parallaxSpeed
      transform += ` translateY(${parallaxOffset}px)`
    }

    return transform
  }

  const getStaggerDelay = () => {
    if (!stagger) return delay
    return delay + staggerDelay
  }

  return (
    <div
      ref={ref}
      className={className}
      style={{
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? getFinalTransform() : getInitialTransform(),
        transition: `all ${duration}ms ${getEaseFunction()} ${getStaggerDelay()}ms`,
        willChange: 'opacity, transform',
      }}
    >
      {children}
    </div>
  )
}

export default AdvancedScrollAnimation
