# 滚动动画组件使用指南

这个目录包含了用于实现类似苹果官网滚动动画效果的组件。

## 组件列表

### 1. ScrollAnimation (基础组件)

最简单的滚动动画组件，支持基本的动画效果。

**属性：**

- `direction`: 动画方向 ('up' | 'down' | 'left' | 'right' | 'fade')
- `delay`: 延迟时间 (毫秒)
- `duration`: 动画持续时间 (毫秒)
- `distance`: 移动距离 (像素)
- `threshold`: 触发阈值 (0-1)
- `className`: 自定义 CSS 类名

**使用示例：**

```tsx
import ScrollAnimation from './components/ScrollAnimation'
;<ScrollAnimation direction="up" delay={200} duration={800}>
  <div className="your-content">你的内容</div>
</ScrollAnimation>
```

### 2. ScrollAnimationManager (高级组件)

更高级的滚动动画组件，支持回调函数和更精细的控制。

**属性：**

- `config`: 动画配置对象
- `onAnimationStart`: 动画开始回调
- `onAnimationComplete`: 动画完成回调

**使用示例：**

```tsx
import ScrollAnimationManager from './components/ScrollAnimationManager'
;<ScrollAnimationManager
  config={{
    id: 'unique-id',
    direction: 'up',
    delay: 200,
    duration: 800,
    distance: 50,
    ease: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  }}
  onAnimationStart={() => console.log('动画开始')}
  onAnimationComplete={() => console.log('动画完成')}
>
  <div className="your-content">你的内容</div>
</ScrollAnimationManager>
```

### 3. AdvancedScrollAnimation (实验性组件)

支持视差滚动和更复杂动画效果的组件。

## 动画方向说明

- `up`: 从下方向上滑动
- `down`: 从上方向下滑动
- `left`: 从右侧向左滑动
- `right`: 从左侧向右滑动
- `fade`: 淡入效果
- `scale`: 缩放效果

## 缓动函数

支持多种缓动函数：

- `cubic-bezier(0.25, 0.46, 0.45, 0.94)` - 平滑缓动
- `cubic-bezier(0.68, -0.55, 0.265, 1.55)` - 弹性缓动
- `cubic-bezier(0.175, 0.885, 0.32, 1.275)` - 弹性缓动 2

## CSS 类名

组件会自动添加以下 CSS 类名：

- `scroll-animation-container`: 动画容器
- `scroll-ease-bounce`: 弹性缓动
- `scroll-ease-elastic`: 弹性缓动 2
- `scroll-ease-smooth`: 平滑缓动

## 性能优化

- 使用 `will-change` 属性优化动画性能
- 支持 `prefers-reduced-motion` 媒体查询
- 响应式动画调整
- 自动清理事件监听器

## 最佳实践

1. **合理设置延迟时间**：避免所有元素同时动画
2. **使用不同的动画方向**：增加视觉层次感
3. **控制动画数量**：避免过度使用动画影响性能
4. **考虑用户偏好**：尊重用户的减少动画设置

## 示例用法

查看 `AnimationExamples.tsx` 文件了解完整的使用示例。

## 注意事项

- 所有动画组件都是客户端组件（使用 'use client'）
- 确保在服务器端渲染时正确处理
- 动画效果在移动设备上会自动调整
- 支持无障碍访问，会尊重用户的动画偏好设置
