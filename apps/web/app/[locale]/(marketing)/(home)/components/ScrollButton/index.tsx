'use client'
import { useEffect, useState } from 'react'

interface ScrollButtonProps {
  targetId: string
  label?: string
}

export default function ScrollButton({
  targetId,
  label = '发现更多创意',
}: ScrollButtonProps) {
  const [mounted, setMounted] = useState(false)
  const [hover, setHover] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const scrollToTarget = () => {
    const target = document.getElementById(targetId)
    if (target) {
      const targetPosition =
        target.getBoundingClientRect().top + window.scrollY - 88
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth',
      })
    }
  }

  if (!mounted) return null

  return (
    <button
      onClick={scrollToTarget}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
      aria-label={label}
      className="relative flex flex-col items-center justify-center w-full animate-bounce bottom-6"
    >
      {/* Hover文字提示 - 添加动画效果 */}
      <div
        className={`absolute bottom-12 transition-all duration-300 whitespace-nowrap ${
          hover ? 'opacity-100' : 'opacity-0'
        }`}
      >
        <span
          className="relative inline-block px-3 py-1 text-sm text-white"
          style={{
            textShadow: '0 1px 2px rgba(0,0,0,0.5)',
          }}
        >
          {/* 文字背景渐变效果 */}
          <span className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 opacity-90"></span>

          {/* 波纹动画效果 */}
          {hover && (
            <div className="absolute top-0 left-0 w-full h-full">
              <span className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 opacity-0 ripple-effect-1"></span>
              <span className="absolute inset-0 rounded-full bg-gradient-to-r from-purple-400 to-blue-400 opacity-0 ripple-effect-2"></span>
            </div>
          )}

          {/* 文字内容 */}
          <span className="relative z-10">{label}</span>
        </span>
      </div>

      {/* 鼠标滚轮动画 */}
      <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center items-start">
        <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
      </div>

      <style jsx>{`
        /* 波纹效果1 */
        @keyframes ripple-1 {
          0% {
            transform: scale(1);
            opacity: 0.3;
          }
          100% {
            transform: scale(1.5);
            opacity: 0;
          }
        }

        /* 波纹效果2 */
        @keyframes ripple-2 {
          0% {
            transform: scale(1);
            opacity: 0.3;
          }
          100% {
            transform: scale(1.7);
            opacity: 0;
          }
        }

        .ripple-effect-1 {
          animation: ripple-1 3s ease-out infinite;
          border-radius: 9999px;
        }

        .ripple-effect-2 {
          animation: ripple-2 3.5s ease-out infinite;
          animation-delay: 1s;
          border-radius: 9999px;
        }
      `}</style>
    </button>
  )
}
