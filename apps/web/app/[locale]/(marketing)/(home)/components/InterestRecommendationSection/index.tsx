import Image from 'next/image'
import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'
import '@/[locale]/(marketing)/(home)/home-gradient-border.css'

export default async function InterestRecommendationSection() {
  const t = await getTranslations()
  const features = [
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-1.jpeg',
      alt: t('relatedTools.backgroundRemover.alt'),
      title: t('relatedTools.backgroundRemover.title'),
      desc: t('relatedTools.backgroundRemover.description'),
    },
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-2.jpeg',
      alt: t('relatedTools.aiBackground.alt'),
      title: t('relatedTools.aiBackground.title'),
      desc: t('relatedTools.aiBackground.description'),
    },
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-3.jpeg',
      alt: t('relatedTools.aiShadows.alt'),
      title: t('relatedTools.aiShadows.title'),
      desc: t('relatedTools.aiShadows.description'),
    },
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-4.png',
      alt: t('relatedTools.aiPhotoEnhancer.alt'),
      title: t('relatedTools.aiPhotoEnhancer.title'),
      desc: t('relatedTools.aiPhotoEnhancer.description'),
    },
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-5.jpeg',
      alt: t('relatedTools.aiHeadshotGenerator.alt'),
      title: t('relatedTools.aiHeadshotGenerator.title'),
      desc: t('relatedTools.aiHeadshotGenerator.description'),
    },
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-6.jpeg',
      alt: t('relatedTools.aiImageUpscaler.alt'),
      title: t('relatedTools.aiImageUpscaler.title'),
      desc: t('relatedTools.aiImageUpscaler.description'),
    },
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-7.jpeg',
      alt: t('relatedTools.aiImageExpander.alt'),
      title: t('relatedTools.aiImageExpander.title'),
      desc: t('relatedTools.aiImageExpander.description'),
    },
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-8.jpeg',
      alt: t('relatedTools.aiImageReplacer.alt'),
      title: t('relatedTools.aiImageReplacer.title'),
      desc: t('relatedTools.aiImageReplacer.description'),
    },
    {
      img: 'https://removeAI-website-static-resources.oss-ap-southeast-1.aliyuncs.com/removeAI-home/x-9.png',
      alt: t('relatedTools.imageToVideoAI.alt'),
      title: t('relatedTools.imageToVideoAI.title'),
      desc: t('relatedTools.imageToVideoAI.description'),
    },
  ]
  return (
    <section className="w-full bg-[#181028] py-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-4xl font-bold text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-4 text-center">
            {t('relatedTools.title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-300 max-w-4xl mx-auto text-center">
            {t('relatedTools.description')}
          </p>
        </div>
        <div className="relative w-full">
          <div className="overflow-x-auto pb-4 pt-4 px-4 hide-scrollbar">
            <div
              className="flex gap-6 md:gap-8"
              style={{
                minWidth: `${
                  features.length * 270 + (features.length - 1) * 24
                }px`,
              }}
            >
              {features.map((f, i) => (
                <div
                  key={i}
                  className="flex-shrink-0 w-[270px] rounded-2xl transition-transform duration-300 border border-[#23203a] hover:border-fuchsia-500/40 hover:scale-105 hover:z-10"
                  style={{
                    transformOrigin:
                      i === 0
                        ? 'left center'
                        : i === features.length - 1
                        ? 'right center'
                        : 'center center',
                  }}
                >
                  <div className="w-full h-[170px] relative rounded-t-2xl overflow-hidden">
                    <Image
                      src={f.img}
                      alt={f.alt}
                      fill
                      className="object-cover"
                      loading="lazy"
                      unoptimized
                    />
                  </div>
                  <div className="p-5 flex flex-col gap-2">
                    <h3 className="text-lg font-bold text-white mb-1">
                      {f.title}
                    </h3>
                    <p className="text-gray-300 text-sm mb-2 min-h-[40px]">
                      {f.desc}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="text-center mt-10">
          <Link
            href="/templates"
            className="inline-block bg-gradient-to-r from-fuchsia-600 via-purple-500 to-indigo-500 text-white px-8 py-3 rounded-lg shadow-lg hover:scale-105 transition-all font-semibold text-lg drop-shadow-[0_2px_16px_rgba(236,72,255,0.5)]"
          >
            {t('exploreAllTools')}
          </Link>
        </div>
      </div>
    </section>
  )
}
