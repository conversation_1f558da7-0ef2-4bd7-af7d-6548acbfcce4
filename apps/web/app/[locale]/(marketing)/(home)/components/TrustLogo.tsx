'use client'

import { useTranslations } from 'next-intl'

const logos = [
  { name: 'TechRadar' },
  { name: '<PERSON>b<PERSON><PERSON>' },
  { name: '<PERSON>' },
  { name: '<PERSON>P<PERSON>' },
  { name: '<PERSON><PERSON>' },
  { name: '<PERSON><PERSON>' },
  { name: '<PERSON><PERSON>' },
  { name: '<PERSON><PERSON><PERSON>' },
  { name: 'Shopify' },
  { name: 'Slack' },
]

const LogoSVG = ({ name }: { name: string }) => (
  <svg
    viewBox="0 0 120 32"
    fill="none"
    className="h-8 w-32 min-w-[120px] cursor-pointer"
    xmlns="http://www.w3.org/2000/svg"
  >
    <text
      x="0"
      y="22"
      fontFamily="Arial Black,Arial,sans-serif"
      fontWeight="bold"
      fontSize="22"
      fill="currentColor"
    >
      {name}
    </text>
  </svg>
)

const SimpleTrustLogo = () => {
  const t = useTranslations()

  return (
    <section className="w-full py-16 bg-transparent">
      <div className="max-w-6xl mx-auto flex flex-col items-center">
        <h2 className="text-2xl md:text-3xl font-bold text-indigo-100 mb-6 text-center">
          {t('home.trustedAndLovedBy')}
        </h2>
        <div
          className="relative w-full overflow-hidden group"
          style={{
            maskImage:
              'linear-gradient(to right, transparent 0%, black 10%, black 90%, transparent 100%)',
          }}
        >
          <div
            className="flex gap-12 animate-scroll-logos will-change-transform"
            style={{ animationPlayState: 'running' }}
          >
            {/* 两遍logo用于无缝滚动 */}
            {[...logos, ...logos].map((logo, idx) => (
              <div
                key={logo.name + idx}
                className="text-gray-400 hover:text-white transition-colors duration-200 opacity-80 hover:opacity-100 flex-shrink-0"
                title={logo.name}
              >
                <LogoSVG name={logo.name} />
              </div>
            ))}
          </div>
          <style jsx>{`
            .animate-scroll-logos {
              animation: scroll-logos 30s linear infinite;
            }
            .group:hover .animate-scroll-logos {
              animation-play-state: paused;
            }
            @keyframes scroll-logos {
              0% {
                transform: translateX(0);
              }
              100% {
                transform: translateX(-50%);
              }
            }
          `}</style>
        </div>
      </div>
    </section>
  )
}

export default SimpleTrustLogo
