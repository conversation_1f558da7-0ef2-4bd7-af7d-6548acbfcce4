import { getTranslations } from 'next-intl/server'
import ImageComparison from '../ImageComparison'

export default async function UserCase() {
  const t = await getTranslations()

  return (
    <div id="home-bottom-area" className="bg-[#181028] py-16 sm:px-28">
      <div className="max-w-6xl mx-auto mb-10">
        <div className="border-gray-200 pt-10 px-4">
          <h2 className="text-3xl md:text-4xl font-bold text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-4 text-center">
            {t('HomeBottomArea.title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-300 max-w-4xl mx-auto text-center">
            {t('HomeBottomArea.description')}
          </p>
        </div>
      </div>

      <div id="image-comparison-section" className="max-w-[1440px] mx-auto">
        <ImageComparison />
      </div>
    </div>
  )
}
