'use client'

import { useState, useRef, useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useDropzone } from 'react-dropzone'
// 
import {
  Loader2,
  UploadCloud,
  X,
  AlertCircle,
  ImageIcon,
  Sparkles,
  Shuffle,
} from 'lucide-react'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { useAtom, useSet<PERSON>tom } from 'jotai'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@ui/components/select'
//
import {
  imagesAtom,
  persistedOssUrlsAtom,
  type ImageItem,
  formDataAtom,
  resetAtom,
  isGeneratedAtom,
  generatedTaskIdAtom,
  formSchema,
  type FormData,
  selectedStyleAtom,
  isStyleCancelledAtom,
  generationProgress<PERSON>tom,
  isGenerating<PERSON><PERSON>,
  generation<PERSON><PERSON><PERSON><PERSON><PERSON>,
  generatedImage<PERSON><PERSON><PERSON><PERSON>,
  generatedImage<PERSON><PERSON><PERSON><PERSON>,
  generation<PERSON>ode<PERSON><PERSON>,
  currentTaskIdAtom,
} from '@marketing/home/<USER>'
import { getUserIdFromCookie } from '@/utils/lib'
import { getUserFromClientCookies } from '@/utils/client-cookies'
import ShowLoginModal from '@shared/components/ShowLoginModal'
import { useTranslations } from 'next-intl'
import PricingSection from '../PricingSection'
import { consumePointsAtom } from '@marketing/stores'
import { useStyles } from '@marketing/home/<USER>/useStyles'
import { usePermissionCheck } from '@shared/hooks/usePermissionCheck'

// API related configuration
const API_URL_GENERATE = '/api/images/generate'
const API_URL_STATUS = '/api/images/record-info'
const POLL_INTERVAL = 5000
const MAX_POLL_TIME = 600000 // 5 minutes
const DEFAULT_MAX_UPLOAD_IMAGES = 5 // 默认最大上传图片数量限制

// LocalStorage keys
const STORAGE_KEY_FORM = 'image_converter_form'
const STORAGE_KEY_MODE = 'image_converter_mode'

// 定义生成模式类型
type GenerationMode = 'text-to-image' | 'image-to-image'

// 安全地从 localStorage 读取数据的辅助函数
const getFromLocalStorage = <T,>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') {
    return defaultValue
  }

  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading ${key} from localStorage:`, error)
    return defaultValue
  }
}

// 安全地将数据写入 localStorage 的辅助函数
const saveToLocalStorage = <T,>(key: string, value: T): void => {
  if (typeof window === 'undefined') {
    return
  }

  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to localStorage:`, error)
  }
}

// 安全地从 sessionStorage 读取数据的辅助函数
const getFromSessionStorage = <T,>(key: string, defaultValue: T): T => {
  if (typeof window === 'undefined') {
    return defaultValue
  }

  try {
    const item = sessionStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error(`Error reading ${key} from sessionStorage:`, error)
    return defaultValue
  }
}

// 安全地将数据写入 sessionStorage 的辅助函数
const saveToSessionStorage = <T,>(key: string, value: T): void => {
  if (typeof window === 'undefined') {
    return
  }

  try {
    sessionStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error(`Error saving ${key} to sessionStorage:`, error)
  }
}

// Custom button component
const Button = ({
  children,
  type = 'button',
  onClick,
  disabled = false,
  className = '',
  variant = 'primary',
}: {
  children: React.ReactNode
  type?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  disabled?: boolean
  className?: string
  variant?: 'primary' | 'secondary' | 'danger' | 'outline'
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-blue-600 hover:bg-blue-700 text-white'
      case 'secondary':
        return 'bg-gray-600 hover:bg-gray-700 text-white'
      case 'danger':
        return 'bg-red-600 hover:bg-red-700 text-white'
      case 'outline':
        return 'bg-white border border-gray-300 hover:bg-gray-50 text-gray-700'
      default:
        return 'bg-blue-600 hover:bg-blue-700 text-white'
    }
  }

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`px-4 py-2 rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${getVariantClasses()} ${className}`}
    >
      {children}
    </button>
  )
}

type ImageStyleConverterProps = {
  onGenerated?: (generatedUrl: string) => void
  onStartGenerating?: () => void
  redirectOnGenerate?: boolean
  onError?: () => void
  maxUploadImages?: number // 最大上传图片数量
  mode?: GenerationMode // 生成模式：文生图或图生图
  defaultMode?: GenerationMode // 默认生成模式
}

export function ImageStyleConverter({
  onGenerated,
  onStartGenerating,
  redirectOnGenerate = false,
  onError,
  maxUploadImages = DEFAULT_MAX_UPLOAD_IMAGES,
  mode,
  defaultMode = 'image-to-image',
}: ImageStyleConverterProps) {
  const t = useTranslations()
  const router = useRouter()
  // 使用整合的图片数据
  const [images, setImages] = useAtom(imagesAtom)
  // 持久化的OSS URL，用于页面刷新后恢复
  const [persistedOssUrls, setPersistedOssUrls] = useAtom(persistedOssUrlsAtom)

  const [formData, setFormData] = useAtom(formDataAtom)
  const [isGenerated, setIsGenerated] = useAtom(isGeneratedAtom)
  const [generatedTaskId, setGeneratedTaskId] = useAtom(generatedTaskIdAtom)
  const [selectedStyle, setSelectedStyle] = useAtom(selectedStyleAtom)
  const [isStyleCancelled, setIsStyleCancelled] = useAtom(isStyleCancelledAtom)
  const [, reset] = useAtom(resetAtom)
  const [showLoginModal, setShowLoginModal] = useState(false)
  const [payDialogTitle, setPayDialogTitle] = useState('')
  const user = getUserFromClientCookies()
  // 使用 Jotai 的 atomWithStorage 来持久化生成模式
  const [generationMode, setGenerationMode] = useAtom(generationModeAtom)
  // 使用 React Query 获取风格数据
  const { data: styles = [], isLoading: isLoadingStyles } = useStyles(
    selectedStyle?.id,
    generationMode
  )

  const { calculatePoints } = usePermissionCheck()
  const points = calculatePoints()

  // 默认表单数据
  const defaultFormValues: FormData = {
    prompt: '',
    ratio: '1:1',
    nVariants: '1',
  }

  // Form configuration - 只使用默认值初始化表单
  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultFormValues,
  })

  // Consume points
  const consumePoints = useSetAtom(consumePointsAtom)

  // Use global state
  const [isGenerating, setIsGenerating] = useAtom(isGeneratingAtom)
  const [generationProgress, setGenerationProgress] = useAtom(
    generationProgressAtom
  )
  const [generationError, setGenerationError] = useAtom(generationErrorAtom)
  const [generatedImageUrl, setGeneratedImageUrl] = useAtom(
    generatedImageUrlAtom
  )
  const [generatedImageUrls, setGeneratedImageUrls] = useAtom(
    generatedImageUrlsAtom
  )

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [currentTaskId, setCurrentTaskId] = useAtom(currentTaskIdAtom)
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const startTimeRef = useRef<number | null>(null)

  // Save generation history
  const saveGenerationHistory = async (
    resultUrls: string[],
    originalImageUrls: string[]
  ) => {
    try {
      const userId = getUserIdFromCookie()

      await fetch('/api/images/save-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          originalImageUrl: originalImageUrls.join('|'), // 将多个URL用 | 连接
          generatedImageUrl: resultUrls.join('|'), // 将多个生成的图片URL用 | 连接
          prompt: form.getValues('prompt'),
          ratio: form.getValues('ratio'),
          extraData: JSON.stringify({
            description: 'Reserved field',
            taskId: currentTaskId,
            originalImageCount: originalImageUrls.length,
            generatedImageCount: resultUrls.length,
            nVariants: form.getValues('nVariants'),
          }),
        }),
      })
    } catch (error) {
      console.error('Failed to save generation history:', error)
    }
  }

  // 从 sessionStorage 获取表单数据
  const savedFormData = useMemo(() => {
    const data = getFromSessionStorage<FormData | null>(STORAGE_KEY_FORM, null)
    console.log('Initial savedFormData from sessionStorage:', data)
    return data
  }, [])

  // 在组件挂载时，确保表单数据从 sessionStorage 中正确加载
  useEffect(() => {
    // 使用 setTimeout 确保在组件完全渲染后执行
    const timer = setTimeout(() => {
      console.log(
        'Loading form data from sessionStorage (delayed):',
        savedFormData
      )

      // 如果有保存的表单数据
      if (savedFormData) {
        // 手动设置表单值，确保它们被正确加载
        if (savedFormData.prompt) {
          console.log('Setting prompt value:', savedFormData.prompt)
          form.setValue('prompt', savedFormData.prompt)
        }
        if (savedFormData.ratio) {
          form.setValue('ratio', savedFormData.ratio)
        }
        if (savedFormData.nVariants) {
          form.setValue('nVariants', savedFormData.nVariants)
        }

        // 同时更新 formData atom
        setFormData(savedFormData)

        // 强制表单重新渲染
        form.trigger()
      }
    }, 100)

    return () => clearTimeout(timer)
  }, [savedFormData, form, setFormData])

  // 检查是否需要自动触发生成
  useEffect(() => {
    // loadData()
    // 使用 setTimeout 确保在组件完全渲染后执行
    const timer = setTimeout(() => {
      // 检查是否需要自动触发生成
      const shouldAutoGenerate = sessionStorage.getItem(
        'auto_generate_on_redirect'
      )
      console.log(
        'Checking auto generate flag in ImageStyleConverter:',
        shouldAutoGenerate
      )

      if (shouldAutoGenerate === 'true') {
        console.log('Auto generating image in ImageStyleConverter')
        // 清除标志，避免重复触发
        sessionStorage.removeItem('auto_generate_on_redirect')

        // 延迟一点时间后自动提交表单
        setTimeout(() => {
          // 从 sessionStorage 获取保存的表单数据
          const savedData = getFromSessionStorage<FormData | null>(
            STORAGE_KEY_FORM,
            null
          )

          console.log('Saved form data from sessionStorage:', savedData)
          console.log('Current form values before setting:', form.getValues())

          // 如果有保存的表单数据，确保表单值正确
          if (savedData) {
            // 手动设置表单值
            if (savedData.prompt) {
              form.setValue('prompt', savedData.prompt)
            }
            if (savedData.ratio) {
              form.setValue('ratio', savedData.ratio)
            }
            if (savedData.nVariants) {
              form.setValue('nVariants', savedData.nVariants)
            }
            console.log('Form values after setting:', form.getValues())
          }

          // 触发表单验证
          form.trigger().then((isValid) => {
            // 再次检查表单是否有效
            const hasValidPrompt = !!form.getValues('prompt')?.trim()

            console.log('Form validation result:', {
              isValid,
              hasValidPrompt,
              isGenerating,
              formValues: form.getValues(),
            })

            if (isValid && hasValidPrompt) {
              console.log('Auto submitting form with values:', form.getValues())

              // 直接调用 onSubmit 函数，传入表单值
              const values = form.getValues()
              onSubmit(values)
            } else {
              console.log(
                'Form not valid or already generating, cannot auto submit'
              )
            }
          })
        }, 500)
      }
    }, 300) // 延长一点时间，确保表单数据已经加载

    return () => clearTimeout(timer)
  }, [])

  // 处理风格选择
  const handleStyleSelect = useCallback(
    (style: any) => {
      setSelectedStyle(style)

      // 更新表单数据
      const newFormData = {
        prompt: style.prompt,
        ratio: form.getValues('ratio') || '3:2',
        nVariants: form.getValues('nVariants') || '1',
      }

      // 更新 formData atom
      setFormData(newFormData)

      // 直接更新表单值
      form.setValue('prompt', style.prompt)

      // 保存到 sessionStorage
      saveToSessionStorage(STORAGE_KEY_FORM, newFormData)

      setIsStyleCancelled(false) // 重置取消状态
    },
    [setSelectedStyle, setFormData, form, setIsStyleCancelled]
  )

  // 随机选择一种风格
  const handleRandomStyle = useCallback(() => {
    console.log('handleRandomStyle executing, styles length:', styles.length)
    if (styles.length > 0) {
      const randomIndex = Math.floor(Math.random() * styles.length)
      const randomStyle = styles[randomIndex]
      console.log('Selected random style:', randomStyle)

      // 直接调用 handleStyleSelect 函数，确保一致的处理逻辑
      handleStyleSelect(randomStyle)
    } else {
      console.log('No styles available for random selection')
    }
  }, [styles, handleStyleSelect])

  // 在组件加载且风格数据加载完成后，检查是否有持久化的风格选择，如果没有则随机选择一个默认风格
  useEffect(() => {
    // 只有当风格数据加载完成时才执行
    if (styles.length > 0) {
      // 如果已经有选中的风格（从localStorage恢复），但是这个风格ID在当前风格列表中不存在
      // 这种情况可能发生在风格列表更新后，之前选中的风格不再可用
      if (
        selectedStyle &&
        !styles.some((style) => style.id === selectedStyle.id)
      ) {
        // 清除无效的选中风格
        setSelectedStyle(null)
      }

      // 如果没有选中的风格且没有手动取消风格选择，则随机选择一个风格
      if (!selectedStyle && !isStyleCancelled) {
        // 使用setTimeout确保在组件完全渲染后执行
        const timer = setTimeout(() => {
          handleRandomStyle()
        }, 100)

        return () => clearTimeout(timer)
      }
    }
  }, [styles, selectedStyle, isStyleCancelled, handleRandomStyle])

  // 添加一个额外的 useEffect 来处理风格数据加载状态变化
  useEffect(() => {
    // 当风格数据从加载中变为加载完成时
    if (
      !isLoadingStyles &&
      styles.length > 0 &&
      !selectedStyle &&
      !isStyleCancelled
    ) {
      // 随机选择一个风格
      handleRandomStyle()
    }
  }, [
    isLoadingStyles,
    styles,
    selectedStyle,
    isStyleCancelled,
    handleRandomStyle,
  ])

  // Listen for selected style, update form prompt
  useEffect(() => {
    if (selectedStyle?.prompt) {
      // 更新表单数据
      const newFormData = {
        prompt: selectedStyle.prompt,
        ratio: form.getValues('ratio') || '3:2',
        nVariants: form.getValues('nVariants') || '1',
      }

      // 更新 formData atom
      setFormData(newFormData)

      // 直接更新表单值
      form.setValue('prompt', selectedStyle.prompt)

      // 保存到 sessionStorage
      saveToSessionStorage(STORAGE_KEY_FORM, newFormData)

      setIsStyleCancelled(false) // Reset cancellation status when selecting a new style
    }
  }, [selectedStyle, form, setFormData, setIsStyleCancelled])

  // 如果传入了 mode 属性，优先使用它
  useEffect(() => {
    // 从 localStorage 获取保存的模式
    const savedMode = getFromLocalStorage<GenerationMode | null>(
      STORAGE_KEY_MODE,
      null
    )

    if (mode) {
      // 如果传入了 mode 属性，优先使用它
      setGenerationMode(mode)
      // 同时保存到 localStorage
      saveToLocalStorage(STORAGE_KEY_MODE, mode)
    } else if (savedMode) {
      // 如果 localStorage 中有保存的模式，使用它
      setGenerationMode(savedMode)
    } else if (defaultMode) {
      // 如果有默认模式，使用它
      setGenerationMode(defaultMode)
      // 同时保存到 localStorage
      saveToLocalStorage(STORAGE_KEY_MODE, defaultMode)
    }
  }, [mode, defaultMode, setGenerationMode])

  // 监听生成模式变化，切换时清空选中的风格
  useEffect(() => {
    // 当生成模式发生变化时，清空当前选中的风格
    if (selectedStyle) {
      // 检查当前选中的风格是否适用于新的模式
      const isStyleCompatible = selectedStyle.type === generationMode

      if (!isStyleCompatible) {
        // 如果当前风格不适用于新模式，清空选择
        setSelectedStyle(null)
        setIsStyleCancelled(false) // 重置取消状态，允许重新随机选择

        // 清空表单的 prompt
        form.setValue('prompt', '')

        // 清空 formData
        const newFormData = {
          prompt: '',
          ratio: form.getValues('ratio') || '1:1',
          nVariants: form.getValues('nVariants') || '1',
        }
        setFormData(newFormData)
        saveToSessionStorage(STORAGE_KEY_FORM, newFormData)
      }
    }

    // 清除生成的图片，重新展示轮播图
    setGeneratedImageUrl(null)
    setGeneratedImageUrls([])
    setIsGenerated(false)
    setGeneratedTaskId(null)
  }, [
    generationMode,
    selectedStyle,
    setSelectedStyle,
    setIsStyleCancelled,
    form,
    setFormData,
    setGeneratedImageUrl,
    setGeneratedImageUrls,
    setIsGenerated,
    setGeneratedTaskId,
  ])

  // 监听风格变化，清除生成的图片
  useEffect(() => {
    // 当风格发生变化时（包括选择新风格或清除风格），清除生成的图片
    setGeneratedImageUrl(null)
    setGeneratedImageUrls([])
    setIsGenerated(false)
    setGeneratedTaskId(null)
  }, [
    selectedStyle,
    setGeneratedImageUrl,
    setGeneratedImageUrls,
    setIsGenerated,
    setGeneratedTaskId,
  ])

  // Listen for style cancellation status
  useEffect(() => {
    if (isStyleCancelled) {
      form.setValue('prompt', '')

      // 清除生成的图片，重新展示轮播图
      setGeneratedImageUrl(null)
      setGeneratedImageUrls([])
      setIsGenerated(false)
      setGeneratedTaskId(null)
    }
  }, [
    isStyleCancelled,
    form,
    setGeneratedImageUrl,
    setGeneratedImageUrls,
    setIsGenerated,
    setGeneratedTaskId,
  ])

  // Clean up polling
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current)
      }
    }
  }, [])

  const resetLoading = useCallback(() => {
    setIsGenerating(false)
    setGenerationProgress(0)
    setIsGenerated(false)
    setGeneratedTaskId(null)
    setIsSubmitting(false)
    setCurrentTaskId(null)
    setGeneratedImageUrl(null)
    setGeneratedImageUrls([])
    onError?.()
  }, [
    setIsGenerating,
    setGenerationProgress,
    setIsGenerated,
    setGeneratedTaskId,
    setIsSubmitting,
    setCurrentTaskId,
    setGeneratedImageUrl,
    setGeneratedImageUrls,
    onError,
  ])

  // Start polling task status
  const startPolling = useCallback(
    (id: string, originalImageUrls: string[]) => {
      // If this task has already been successfully generated, return directly
      if (generatedTaskId === id) {
        setIsSubmitting(false)
        setCurrentTaskId(null)
        return
      }

      // 确保每次开始轮询时都重置开始时间
      startTimeRef.current = Date.now()
      console.log(
        '开始轮询任务:',
        id,
        '开始时间:',
        new Date(startTimeRef.current).toLocaleString()
      )

      const pollForResult = async () => {
        try {
          const response = await fetch(`${API_URL_STATUS}?taskId=${id}`, {
            method: 'GET',
            headers: {
              Accept: 'application/json',
            },
          })

          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`)
          }

          const data = await response.json()

          if (data.code === 200) {
            if (data.data.status === 'SUCCESS' && data.data.successFlag === 1) {
              // Generation successful
              const resultUrls = data.data.response.resultUrls || []

              // 保存所有生成的图片URL
              setGeneratedImageUrls(resultUrls)

              // 为了兼容性，仍然设置第一张图片为主图片
              const resultUrl = resultUrls.length > 0 ? resultUrls[0] : null
              setIsGenerating(false)
              setGenerationProgress(100)
              setIsGenerated(true)
              setGeneratedTaskId(id)

              if (resultUrls.length > 0) {
                // 设置第一张图片为主图片（兼容旧版本）
                if (resultUrl) {
                  setGeneratedImageUrl(resultUrl)
                }

                // Only save history when first generation is successful
                if (generatedTaskId !== id) {
                  await saveGenerationHistory(resultUrls, originalImageUrls)
                }

                if (onGenerated && resultUrl) {
                  onGenerated(resultUrl)
                }
              }

              setIsSubmitting(false)
              setCurrentTaskId(null)

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else if (
              data.data.status === 'FAILED' ||
              (data.data.status === 'GENERATE_FAILED' &&
                data.data.successFlag === 3)
            ) {
              // Generation failed
              setGenerationError(
                `Generation failed: ${
                  data.data.errorMessage || 'Unknown error'
                }`
              )
              resetLoading()

              if (pollIntervalRef.current) {
                clearInterval(pollIntervalRef.current)
                pollIntervalRef.current = null
              }
            } else {
              // Still processing, update progress
              const elapsed = Date.now() - (startTimeRef.current || Date.now())
              const progress = Number(data.data.progress) * 100

              // 确保进度只能向前增长，不会倒退
              setGenerationProgress((currentProgress) => {
                const newProgress = Math.max(currentProgress, progress)
                return newProgress
              })

              // Check if timeout
              if (elapsed > MAX_POLL_TIME) {
                console.log('Generation timed out, please try again')
                setGenerationError(t('ImageStyleConverter.generationTimeout'))
                resetLoading()

                if (pollIntervalRef.current) {
                  clearInterval(pollIntervalRef.current)
                  pollIntervalRef.current = null
                }
              }
            }
          } else {
            throw new Error(data.msg || 'Request failed')
          }
        } catch (err) {
          console.error('Error polling for result:', err)
          setGenerationError(t('ImageStyleConverter.generationError'))
          resetLoading()

          if (pollIntervalRef.current) {
            clearInterval(pollIntervalRef.current)
            pollIntervalRef.current = null
          }
        }
      }

      // Execute immediately, then set timer
      pollForResult()
      pollIntervalRef.current = setInterval(pollForResult, POLL_INTERVAL)
    },
    [
      onGenerated,
      setIsGenerated,
      setGeneratedTaskId,
      generatedTaskId,
      resetLoading,
      setIsGenerating,
      setGenerationProgress,
      setGenerationError,
      setGeneratedImageUrl,
    ]
  )

  // Check if form is valid
  const isFormValid = form.watch('prompt')?.trim()

  // Form submission handling
  const onSubmit = useCallback(
    async (values: FormData) => {
      if (!values.prompt.trim()) {
        setGenerationError(t('ImageStyleConverter.promptRequired'))
        return
      }

      // Check points first
      if (!user) {
        setShowLoginModal(true)
        return
      }

      // If already submitting or there's an ongoing task, don't submit again
      if (isSubmitting || currentTaskId) {
        return
      }

      setGenerationError(null)
      setIsSubmitting(true)
      setIsGenerating(true)

      // 只有在没有当前任务ID时才重置进度为5%（表示这是一个新任务）
      if (!currentTaskId) {
        setGenerationProgress(5)
      }

      onStartGenerating?.()

      try {
        let filesUrl: string[] = []

        // 如果是图生图模式且有上传的图片
        if (generationMode === 'image-to-image' && images.length > 0) {
          // 收集所有已上传完成的图片的 OSS URL
          const ossUrls = images
            .filter((image) => image.ossUrl) // 只选择已有 OSS URL 的图片
            .map((image) => image.ossUrl as string)

          // 检查是否所有图片都已上传完成
          if (ossUrls.length === images.length) {
            // 所有图片都已上传完成，直接使用 OSS URL
            filesUrl = ossUrls
          } else {
            // 如果有图片还未上传完成，收集所有图片文件对象
            const files = images
              .filter((image) => image.file) // 只选择有文件对象的图片
              .map((image) => image.file as File)

            // 重新上传所有图片
            filesUrl = await uploadMultipleToOSS(files)
          }
        }

        // 如果是图生图模式但没有上传图片，显示错误
        if (generationMode === 'image-to-image' && images.length === 0) {
          setGenerationError(
            'Please upload at least one image for Image to Image mode'
          )
          setIsSubmitting(false)
          return
        }

        const response = await fetch(API_URL_GENERATE, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify({
            filesUrl,
            prompt: values.prompt,
            size: values.ratio,
            callBackUrl: 'https://your-callback-url.com/callback',
            mode: generationMode, // 添加生成模式参数
            nVariants: values.nVariants || '1', // 添加变体数量参数
          }),
        })

        if (!response.ok) {
          console.log(`HTTP error! Status: ${response.status}`)
        }

        const data = await response.json()

        if (data.code === 401000) {
          setShowLoginModal(true)
          resetLoading()
          return
        }

        if (
          data.code === 400000 &&
          data.message?.en === 'Insufficient points.'
        ) {
          resetLoading()
          setPayDialogTitle(t('ImageStyleConverter.insufficientPoints'))
          return
        }

        if (data.code === 100000) {
          const newTaskId = data.data.taskId
          setCurrentTaskId(newTaskId)
          startPolling(newTaskId, filesUrl)

          // 根据不同模式消耗积分（这里假设两种模式消耗相同积分，实际可能不同）
          consumePoints(points)
        } else {
          throw new Error(data.msg || 'Request failed')
        }
      } catch (err) {
        console.error('Generation request failed:', err)
        setGenerationError(
          'Failed to initiate generation request, please try again'
        )
        resetLoading()
      }
    },
    [
      images,
      persistedOssUrls,
      generationMode,
      router,
      setFormData,
      onStartGenerating,
      startPolling,
      resetLoading,
      isSubmitting,
    ]
  )

  // Listen for form changes
  useEffect(() => {
    const subscription = form.watch((values, { name }) => {
      console.log('Form value changed:', name, values)

      if (name === 'prompt' || name === 'ratio' || name === 'nVariants') {
        setIsGenerated(false)

        // 保存表单数据到 sessionStorage
        if (values.prompt || values.ratio || values.nVariants) {
          const formData = {
            prompt: values.prompt || '',
            ratio: values.ratio || '3:2',
            nVariants: values.nVariants || '1',
          }
          console.log('Saving form data to sessionStorage:', formData)
          saveToSessionStorage(STORAGE_KEY_FORM, formData)

          // 同时更新 formData atom
          setFormData(formData)
        }
      }
    })
    return () => subscription.unsubscribe()
  }, [form, setIsGenerated, setFormData])

  // Clean up when component unmounts
  useEffect(() => {
    return () => {
      if (pollIntervalRef.current) {
        clearInterval(pollIntervalRef.current)
        pollIntervalRef.current = null
      }
      // 不再清除 currentTaskId，保留给下次挂载使用
      setIsSubmitting(false)
    }
  }, [])

  // 组件挂载时检查是否有未完成的任务，若有则重新开始轮询
  useEffect(() => {
    // 只在组件挂载时执行一次
    if (currentTaskId && isGenerating) {
      console.log('检测到未完成的任务，重新开始轮询:', currentTaskId)
      setIsGenerating(true)

      // 只有在进度为0时才设置为5%，避免覆盖已有的进度
      setGenerationProgress((currentProgress) => {
        return currentProgress === 0 ? 5 : currentProgress
      })

      // 由于可能无法获取到原始的上传图片，传入当前图片数据的OSS URLs
      const currentOssUrls = images
        .filter((img) => img.ossUrl)
        .map((img) => img.ossUrl as string)

      // 重新开始轮询
      startPolling(currentTaskId, currentOssUrls)

      // 如果有回调，触发开始生成的回调
      onStartGenerating?.()
    }
  }, [])

  // 组件加载时，从持久化的 OSS URL 恢复图片
  useEffect(() => {
    const restoreImagesFromOss = async () => {
      // 如果有持久化的 OSS URL，则使用它们创建图片数据
      if (persistedOssUrls.length > 0 && images.length === 0) {
        try {
          console.log('Restoring images from OSS URLs:', persistedOssUrls)

          // 创建新的图片数据数组
          const restoredImages: ImageItem[] = persistedOssUrls.map(
            (ossUrl) => ({
              previewUrl: ossUrl, // 使用 OSS URL 作为预览
              ossUrl: ossUrl, // 保存 OSS URL
              uploading: false, // 标记为已完成上传
            })
          )

          // 更新图片数据
          setImages(restoredImages)
        } catch (error) {
          console.error('Failed to restore images from OSS URLs:', error)
        }
      }
    }

    // 在组件挂载时恢复图片
    restoreImagesFromOss()
  }, [persistedOssUrls, images.length, setImages])

  // File upload handling
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length === 0) return

      // 获取图片尺寸的函数
      const getImageDimensions = (
        url: string
      ): Promise<{ width: number; height: number }> => {
        return new Promise((resolve, reject) => {
          const img = document.createElement('img')
          img.onload = () => {
            resolve({
              width: img.width,
              height: img.height,
            })
          }
          img.onerror = reject
          img.src = url
        })
      }

      // 检查是否超过最大图片数量限制
      const remainingSlots = maxUploadImages - images.length

      if (remainingSlots <= 0) {
        setGenerationError(
          `You can upload a maximum of ${maxUploadImages} images`
        )
        return
      }

      // 限制添加的文件数量
      const filesToAdd = acceptedFiles.slice(0, remainingSlots)

      // 如果尝试上传的图片超过限制，显示提示
      if (acceptedFiles.length > remainingSlots) {
        setGenerationError(
          `Maximum upload limit reached (${maxUploadImages} images). Only added the first ${remainingSlots} image(s)`
        )
      } else {
        setGenerationError(null)
      }

      // 创建新的图片数据数组
      const newImages = [...images]

      // 处理每个文件
      for (const file of filesToAdd) {
        try {
          // 创建本地预览
          const filePreview = URL.createObjectURL(file)

          // 创建新的图片数据项
          const newImageItem: ImageItem = {
            file, // 保存文件对象
            previewUrl: filePreview, // 使用本地预览 URL
            uploading: true, // 标记为正在上传
          }

          // 添加到图片数组
          newImages.push(newImageItem)

          // 获取当前图片的索引
          const currentIndex = newImages.length - 1

          // 在后台上传到 OSS - 使用闭包捕获当前索引
          const uploadCurrentFile = async (
            fileToUpload: File,
            fileIndex: number
          ) => {
            try {
              // 上传文件到 OSS
              const ossUrl = await uploadToOSS(fileToUpload)

              // 上传成功，更新图片数据
              setImages((prevImages) => {
                const updatedImages = [...prevImages]
                // 确保索引有效
                if (fileIndex < updatedImages.length) {
                  // 更新当前图片的数据
                  updatedImages[fileIndex] = {
                    ...updatedImages[fileIndex],
                    ossUrl, // 保存 OSS URL
                    uploading: false, // 标记为已完成上传
                  }
                }
                return updatedImages
              })

              // 更新持久化的 OSS URL 数组
              setPersistedOssUrls((prevUrls) => {
                const newUrls = [...prevUrls]
                // 确保数组长度足够
                while (newUrls.length <= fileIndex) {
                  newUrls.push('')
                }
                newUrls[fileIndex] = ossUrl
                return newUrls
              })
            } catch (error) {
              // 上传失败，更新图片数据
              setImages((prevImages) => {
                const updatedImages = [...prevImages]
                // 确保索引有效
                if (fileIndex < updatedImages.length) {
                  // 更新当前图片的数据
                  updatedImages[fileIndex] = {
                    ...updatedImages[fileIndex],
                    uploading: false, // 标记为已完成上传
                    error: 'Upload failed', // 添加错误信息
                  }
                }
                return updatedImages
              })
              console.error(
                `Failed to upload image at index ${fileIndex}:`,
                error
              )
            }
          }

          // 启动上传
          uploadCurrentFile(file, currentIndex)

          // 只对第一张图片设置比例（如果之前没有图片）
          if (images.length === 0 && newImages.length === 1) {
            const { width, height } = await getImageDimensions(filePreview)
            const ratio = width / height

            // Preset ratio actual values
            const ratioValues = {
              '3:2': 1.5,
              '2:3': 0.667,
              '1:1': 1,
            } as const

            // Find closest ratio
            let closestRatio: '3:2' | '2:3' | '1:1' = '1:1'
            let minDiff = Infinity

            Object.entries(ratioValues).forEach(([key, value]) => {
              const diff = Math.abs(ratio - value)
              if (diff < minDiff) {
                minDiff = diff
                closestRatio = key as '3:2' | '2:3' | '1:1'
              }
            })

            // Set form ratio value
            form.setValue('ratio', closestRatio)
          }
        } catch (err) {
          console.error('Error getting image dimensions:', err)
        }
      }

      // 更新图片数据
      setImages(newImages)
      setIsGenerated(false)
      setGeneratedTaskId(null) // Reset generated taskId
    },
    [
      images,
      setImages,
      setPersistedOssUrls,
      setIsGenerated,
      setGeneratedTaskId,
      setGenerationError,
      form,
      maxUploadImages,
    ]
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/webp': ['.webp'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    // 允许上传多张图片
  })

  // Remove uploaded image
  const removeImage = useCallback(
    (index?: number) => {
      if (typeof index === 'number') {
        // 删除特定图片
        if (images[index]?.previewUrl) {
          // 如果是本地预览 URL（以 blob: 开头），则需要释放
          if (images[index].previewUrl.startsWith('blob:')) {
            URL.revokeObjectURL(images[index].previewUrl)
          }
        }

        // 更新图片数据 - 删除指定索引的图片
        setImages((prevImages) => {
          const newImages = [...prevImages]
          newImages.splice(index, 1)
          return newImages
        })

        // 更新持久化的 OSS URL 数组
        setPersistedOssUrls((prevUrls) => {
          const newUrls = [...prevUrls]
          newUrls.splice(index, 1)
          return newUrls
        })
      } else {
        // 删除所有图片
        images.forEach((image) => {
          // 如果是本地预览 URL（以 blob: 开头），则需要释放
          if (image.previewUrl && image.previewUrl.startsWith('blob:')) {
            URL.revokeObjectURL(image.previewUrl)
          }
        })

        // 清空所有图片数据
        setImages([])
        setPersistedOssUrls([])
        setIsGenerated(false)
        setGeneratedTaskId(null)
      }
    },
    [images, setImages, setPersistedOssUrls, setIsGenerated, setGeneratedTaskId]
  )

  // Upload image to OSS (client-side direct upload)
  const uploadToOSS = async (file: File): Promise<string> => {
    try {
      // 使用客户端直接上传
      return await clientDirectUpload(file)
    } catch (error) {
      console.error('Failed to upload to OSS:', error)
      setGenerationError('Failed to upload image, please try again')
      throw new Error('Failed to upload image')
    }
  }

  // 客户端直接上传方式
  const clientDirectUpload = async (file: File): Promise<string> => {
    try {
      console.log('Using client direct upload method')
      // 1. 获取签名 URL
      const signatureResponse = await fetch('/api/upload/get-signature', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: file.name,
          contentType: file.type,
        }),
      })

      if (!signatureResponse.ok) {
        console.error(
          'Failed to get upload signature, status:',
          signatureResponse.status
        )
        throw new Error('Failed to get upload signature')
      }

      const signatureData = await signatureResponse.json()

      if (signatureData.code !== 200) {
        console.error('Signature API returned error:', signatureData.msg)
        throw new Error(signatureData.msg || 'Failed to get upload signature')
      }

      const { url: signedUrl, ossUrl } = signatureData.data

      if (!signedUrl || !ossUrl) {
        console.error('Invalid signature data:', signatureData)
        throw new Error('Invalid signature data received')
      }

      // 2. 使用签名 URL 直接上传文件到 OSS
      const uploadResponse = await fetch(signedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      })

      if (!uploadResponse.ok) {
        console.error('Failed to upload to OSS, status:', uploadResponse.status)
        throw new Error('Upload failed')
      }

      // 3. 返回 OSS URL
      return ossUrl
    } catch (error) {
      console.error('Client direct upload failed:', error)
      throw new Error('Client direct upload failed')
    }
  }

  // 上传多个图片到OSS
  const uploadMultipleToOSS = async (files: File[]): Promise<string[]> => {
    const uploadPromises = files.map((file) => uploadToOSS(file))
    return Promise.all(uploadPromises)
  }

  useEffect(() => {
    setGenerationError(null)
  }, [])

  return (
    <div
      className={`w-full ${
        !redirectOnGenerate ? 'max-w-md mx-auto' : ''
      } image-style-converter`}
    >
      <div
        className={`${
          !redirectOnGenerate
            ? 'bg-white rounded-lg shadow-md overflow-hidden'
            : ''
        }`}
      >
        <div className={redirectOnGenerate ? '' : 'p-4'}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Generation Mode Selector */}
            <div className="mb-4">
              <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                <button
                  type="button"
                  onClick={() => {
                    setGenerationMode('image-to-image')
                    // 使用 localStorage 保存模式选择
                    saveToLocalStorage(STORAGE_KEY_MODE, 'image-to-image')
                  }}
                  className={`flex-1 py-2 px-3 text-sm font-medium transition-colors ${
                    generationMode === 'image-to-image'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                  } ${isGenerating ? 'cursor-not-allowed' : ''}`}
                  disabled={isGenerating}
                >
                  Image to Image
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setGenerationMode('text-to-image')
                    // 使用 localStorage 保存模式选择
                    saveToLocalStorage(STORAGE_KEY_MODE, 'text-to-image')
                  }}
                  className={`flex-1 py-2 px-3 text-sm font-medium transition-colors ${
                    generationMode === 'text-to-image'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                  } ${isGenerating ? 'cursor-not-allowed' : ''}`}
                  disabled={isGenerating}
                >
                  Text to Image
                </button>
              </div>
            </div>

            {/* 移除独立的风格预览区域，将其整合到风格选择部分 */}

            {/* Image upload area - only show in image-to-image mode */}
            {generationMode === 'image-to-image' && (
              <>
                {images.length === 0 ? (
                  <div
                    {...getRootProps()}
                    className={`border-2 border-dashed rounded-xl p-4 text-center cursor-pointer transition-all ${
                      isDragActive
                        ? 'border-blue-500 bg-blue-50/50'
                        : redirectOnGenerate
                        ? 'border-gray-300 hover:border-blue-400 hover:bg-gray-50/30 backdrop-blur-sm'
                        : 'border-gray-300 hover:border-blue-300 hover:bg-gray-50'
                    }`}
                  >
                    <input {...getInputProps()} />
                    <div className="flex flex-col items-center justify-center h-[144px]">
                      <UploadCloud
                        className={`h-8 w-8 ${
                          redirectOnGenerate ? 'text-gray-500' : 'text-gray-400'
                        } mb-1`}
                      />
                      <p className="font-medium text-gray-700">
                        {t('ImageStyleConverter.uploadButton')}
                      </p>
                      <p className="text-xs text-gray-500 mt-0.5">
                        {t('ImageStyleConverter.uploadDesc')}
                      </p>
                      <p className="text-xs text-blue-500 font-medium mt-2">
                        Upload up to {maxUploadImages} images
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <p className="text-sm font-medium text-gray-700">
                        Uploaded {images.length}/{maxUploadImages} images
                      </p>
                      <div className="flex gap-2">
                        <button
                          type="button"
                          className={`text-xs ${
                            images.length >= maxUploadImages
                              ? 'text-gray-400 cursor-not-allowed'
                              : 'text-blue-600 hover:text-blue-800'
                          }`}
                          onClick={() => {
                            if (images.length < maxUploadImages) {
                              const dropzoneElement = document.querySelector(
                                '[data-testid="dropzone"]'
                              )
                              if (dropzoneElement) {
                                // @ts-ignore
                                dropzoneElement.click()
                              }
                            }
                          }}
                          disabled={
                            isGenerating || images.length >= maxUploadImages
                          }
                        >
                          Add more
                        </button>
                        <button
                          type="button"
                          className="text-xs text-red-600 hover:text-red-800"
                          onClick={() => removeImage()}
                          disabled={isGenerating}
                        >
                          Clear all
                        </button>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
                      {images.map((image, index) => (
                        <div
                          key={index}
                          className={`relative rounded-md overflow-hidden border shadow-sm ${
                            redirectOnGenerate
                              ? 'border-gray-100 bg-gray-50/30 backdrop-blur-sm'
                              : 'border-gray-100 hover:border-gray-200'
                          }`}
                        >
                          <div className="relative w-full h-[100px]">
                            <Image
                              // 使用图片的预览 URL
                              src={image.previewUrl}
                              alt={`Preview image ${index + 1}`}
                              fill
                              className="object-contain"
                              unoptimized
                            />

                            {/* 上传状态指示器 */}
                            {image.uploading && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                                <div className="h-8 w-8 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                              </div>
                            )}

                            {/* 上传错误指示器 */}
                            {image.error && (
                              <div className="absolute bottom-1 left-1 bg-red-500 text-white text-xs px-1 rounded">
                                Upload failed
                              </div>
                            )}
                          </div>
                          <button
                            type="button"
                            className="absolute top-1 right-1 h-5 w-5 rounded-full bg-black/70 flex items-center justify-center text-white hover:bg-black/80 transition-colors"
                            onClick={(e) => {
                              e.stopPropagation()
                              removeImage(index)
                            }}
                            disabled={isGenerating}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </div>
                      ))}

                      {images.length < maxUploadImages && (
                        <div
                          {...getRootProps()}
                          className={`border border-dashed rounded-md p-2 text-center cursor-pointer transition-all h-[100px] flex items-center justify-center shadow-sm ${
                            isDragActive
                              ? 'border-blue-400 bg-blue-50/50'
                              : redirectOnGenerate
                              ? 'border-gray-200 hover:border-blue-300 hover:bg-gray-50/30 backdrop-blur-sm'
                              : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50'
                          }`}
                          data-testid="dropzone"
                        >
                          <input {...getInputProps()} />
                          <div className="flex flex-col items-center justify-center">
                            <UploadCloud className="h-6 w-6 text-gray-400 mb-1" />
                            <p className="text-xs text-gray-500">
                              Add more images
                            </p>
                            <p className="text-xs text-gray-400">
                              {maxUploadImages - images.length} more allowed
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Style Selection with Preview */}
            <div className="space-y-3 mb-4">
              <div className="flex items-center justify-between">
                <h2 className="text-sm font-medium text-gray-800">
                  Style Selection
                </h2>
                {selectedStyle && (
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedStyle(null)
                      form.setValue('prompt', '')
                      setIsStyleCancelled(true)
                    }}
                    className={`flex items-center gap-1 px-2 py-1 rounded-md border transition-colors ${
                      isGenerating
                        ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed opacity-50'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border-gray-300'
                    }`}
                    title="Cancel style selection"
                    disabled={isGenerating}
                  >
                    <X className="h-4 w-4 text-gray-700" />
                    <span className="text-xs font-medium">Clear Style</span>
                  </button>
                )}
              </div>

              <div className="bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                <div className="flex flex-col gap-4">
                  {/* 风格选择控件 */}
                  <div className="w-full">
                    {isLoadingStyles ? (
                      <div className="h-9 w-full flex items-center justify-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500"></div>
                      </div>
                    ) : (
                      <div className="flex gap-2">
                        <div className="flex-1">
                          <Select
                            value={selectedStyle?.id?.toString() || ''}
                            onValueChange={(value) => {
                              const style = styles.find(
                                (s) => s.id.toString() === value
                              )
                              if (style) handleStyleSelect(style)
                            }}
                            disabled={isGenerating}
                          >
                            <SelectTrigger
                              className={`w-full ${
                                isGenerating
                                  ? 'opacity-70 text-gray-400 bg-gray-50 cursor-not-allowed'
                                  : ''
                              }`}
                            >
                              <div className="flex items-center gap-2">
                                <Sparkles
                                  className={`h-4 w-4 ${
                                    isGenerating
                                      ? 'text-gray-400'
                                      : 'text-blue-500'
                                  }`}
                                />
                                <SelectValue placeholder="Select a style" />
                              </div>
                            </SelectTrigger>
                            <SelectContent>
                              {styles.map((style) => (
                                <SelectItem
                                  key={style.id}
                                  value={style.id.toString()}
                                >
                                  {style.title}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* 随机风格按钮 */}
                        <button
                          type="button"
                          onClick={handleRandomStyle}
                          className={`flex items-center justify-center h-9 px-3 rounded-md border transition-colors ${
                            isGenerating
                              ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed opacity-50'
                              : 'border-gray-300 bg-white hover:bg-gray-50 text-gray-700'
                          }`}
                          title="Random Style"
                          disabled={isGenerating}
                        >
                          <Shuffle
                            className={`h-4 w-4 ${
                              isGenerating ? 'text-gray-400' : 'text-blue-500'
                            }`}
                          />
                        </button>
                      </div>
                    )}
                  </div>

                  {/* 风格预览 - 在两种模式下都显示 */}
                  {selectedStyle &&
                    selectedStyle.ratio &&
                    selectedStyle?.generatedImage && (
                      <div className="w-full">
                        <div className="flex justify-center">
                          <div
                            className={`bg-gray-50 rounded-lg overflow-hidden ${
                              redirectOnGenerate
                                ? 'border border-gray-100'
                                : 'border border-gray-200'
                            }`}
                          >
                            {generationMode === 'image-to-image' ? (
                              // 图生图模式：显示原图和效果图对比
                              <div
                                className="grid grid-cols-2 gap-1"
                                style={{
                                  maxHeight: '120px',
                                }}
                              >
                                <div
                                  className="relative"
                                  style={{
                                    aspectRatio: selectedStyle.ratio.replace(
                                      ':',
                                      '/'
                                    ),
                                    height:
                                      selectedStyle.ratio === '2:3'
                                        ? '120px'
                                        : '80px',
                                    width:
                                      selectedStyle.ratio === '2:3'
                                        ? '80px'
                                        : '120px',
                                  }}
                                >
                                  <Image
                                    src={selectedStyle.originalImage}
                                    alt="Original image"
                                    fill
                                    className="object-contain bg-gray-100"
                                  />
                                  <div className="absolute bottom-1 left-1 bg-black/50 text-white text-[10px] px-1 py-0.5 rounded">
                                    {t('ImageStyleConverter.original')}
                                  </div>
                                </div>
                                <div
                                  className="relative"
                                  style={{
                                    aspectRatio: selectedStyle.ratio.replace(
                                      ':',
                                      '/'
                                    ),
                                    height:
                                      selectedStyle.ratio === '2:3'
                                        ? '120px'
                                        : '80px',
                                    width:
                                      selectedStyle.ratio === '2:3'
                                        ? '80px'
                                        : '120px',
                                  }}
                                >
                                  <Image
                                    src={selectedStyle.generatedImage}
                                    alt="Generated effect"
                                    fill
                                    className="object-contain bg-gray-100"
                                  />
                                  <div className="absolute bottom-1 left-1 bg-black/50 text-white text-[10px] px-1 py-0.5 rounded">
                                    {t('ImageStyleConverter.effect')}
                                  </div>
                                </div>
                              </div>
                            ) : (
                              // 文生图模式：只显示生成的效果图
                              <div className="p-1">
                                <div
                                  className="relative"
                                  style={{
                                    aspectRatio: selectedStyle.ratio.replace(
                                      ':',
                                      '/'
                                    ),
                                    height:
                                      selectedStyle.ratio === '2:3'
                                        ? '120px'
                                        : '80px',
                                    width:
                                      selectedStyle.ratio === '2:3'
                                        ? '80px'
                                        : '120px',
                                  }}
                                >
                                  <Image
                                    src={selectedStyle.generatedImage}
                                    alt="Style preview"
                                    fill
                                    className="object-contain bg-gray-100"
                                  />
                                  <div className="absolute bottom-1 left-1 bg-black/50 text-white text-[10px] px-1 py-0.5 rounded">
                                    Preview
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                </div>
              </div>
            </div>

            {/* Prompt input */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                {t('ImageStyleConverter.conversionPrompt')}
                <span className="text-red-500 ml-0.5">*</span>
              </label>
              <textarea
                {...form.register('prompt')}
                placeholder={t('ImageStyleConverter.conversionPromptDesc')}
                className={`w-full min-h-[100px] resize-y rounded-xl border px-3 py-2 shadow-sm focus:outline-none focus:ring-1 ${
                  redirectOnGenerate ? 'bg-gray-50/30 backdrop-blur-sm' : ''
                } ${
                  form.formState.errors.prompt ||
                  (!form.watch('prompt')?.trim() &&
                    form.formState.touchedFields.prompt)
                    ? 'border-red-300 focus:border-red-500 focus:ring-red-500'
                    : 'border-gray-200 hover:border-gray-300 focus:border-blue-500 focus:ring-blue-500'
                } ${
                  isGenerating
                    ? 'opacity-70 text-gray-400 cursor-not-allowed placeholder:text-gray-300 bg-gray-50'
                    : ''
                }`}
                disabled={isGenerating}
              />
              {(form.formState.errors.prompt ||
                (!form.watch('prompt')?.trim() &&
                  form.formState.touchedFields.prompt)) && (
                <p className="text-sm text-red-600">
                  {t('ImageStyleConverter.promptRequired')}
                </p>
              )}
            </div>

            {/* Output settings section */}
            <div className="space-y-3">
              <h2 className="text-sm font-medium text-gray-800">
                Output Settings
              </h2>

              {/* Output ratio and variants count selection in one row */}
              <div className="flex flex-row gap-4 bg-gray-50/70 p-4 rounded-xl border border-gray-100">
                {/* Output ratio selection */}
                <div className="flex-1 space-y-2">
                  <div>
                    <h3 className="text-sm font-medium text-gray-800">
                      {t('ImageStyleConverter.outputRatio')}
                    </h3>
                  </div>

                  <div className="w-full">
                    <Select
                      value={form.watch('ratio')}
                      onValueChange={(value) =>
                        form.setValue('ratio', value as '3:2' | '2:3' | '1:1')
                      }
                      disabled={isGenerating}
                    >
                      <SelectTrigger
                        className={`w-full ${
                          isGenerating
                            ? 'opacity-70 text-gray-400 bg-gray-50 cursor-not-allowed'
                            : ''
                        }`}
                      >
                        <SelectValue placeholder="Select ratio" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="3:2">
                          <div className="flex items-center gap-3">
                            <div className="w-10 flex items-center justify-center">
                              <div className="w-5 h-3.5 border-2 border-gray-600"></div>
                            </div>
                            <span>3:2</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="1:1">
                          <div className="flex items-center gap-3">
                            <div className="w-10 flex items-center justify-center">
                              <div className="w-3.5 h-3.5 border-2 border-gray-600"></div>
                            </div>
                            <span>1:1</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="2:3">
                          <div className="flex items-center gap-3">
                            <div className="w-10 flex items-center justify-center">
                              <div className="w-3.5 h-5 border-2 border-gray-600"></div>
                            </div>
                            <span>2:3</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {form.formState.errors.ratio && (
                      <p className="text-sm text-red-600 mt-1">
                        {form.formState.errors.ratio.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Variants count selection */}
                <div className="flex-1 space-y-2">
                  <div>
                    <h3 className="text-sm font-medium text-gray-800">
                      Number of Images
                    </h3>
                  </div>

                  <div className="w-full">
                    <Select
                      value={form.watch('nVariants')}
                      onValueChange={(value) =>
                        form.setValue('nVariants', value as '1' | '2' | '4')
                      }
                      disabled={isGenerating}
                    >
                      <SelectTrigger
                        className={`w-full ${
                          isGenerating
                            ? 'opacity-70 text-gray-400 bg-gray-50 cursor-not-allowed'
                            : ''
                        }`}
                      >
                        <div className="flex items-center gap-2">
                          <ImageIcon
                            className={`h-4 w-4 ${
                              isGenerating ? 'text-gray-400' : ''
                            }`}
                          />
                          <SelectValue placeholder="Select quantity" />
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 Image</SelectItem>
                        <SelectItem value="2">2 Images</SelectItem>
                        <SelectItem value="4">4 Images</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </div>

            {/* Generate button and progress */}
            <div className="space-y-4">
              <Button type="submit" className="w-full" disabled={!isFormValid}>
                {isGenerating ? (
                  <div className="flex items-center justify-center">
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    <span>{t('ImageStyleConverter.generating')}</span>
                  </div>
                ) : (
                  t('ImageStyleConverter.generateButton')
                )}
              </Button>

              {generationError && (
                <div
                  className={`bg-red-50 p-3 rounded-md flex items-start ${
                    redirectOnGenerate ? 'bg-opacity-80' : ''
                  }`}
                >
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-600">{generationError}</p>
                </div>
              )}
            </div>
          </form>
        </div>
      </div>

      {showLoginModal && (
        <ShowLoginModal
          title={t('loginTipsTitle')}
          desc={t('tipLogin')}
          onClose={() => setShowLoginModal(false)}
        />
      )}

      <Dialog
        open={!!payDialogTitle}
        onOpenChange={(open) =>
          setPayDialogTitle((title) => (open ? title : ''))
        }
      >
        <DialogContent
          className="
          max-w-[1200px]
          w-full
          h-[920px]
          bg-[#1A1B1E]
          border-[#2D2E32]
          shadow-xl
        "
        >
          <DialogHeader>
            <DialogTitle className="text-white">
              {t('insufficientCredits')}
            </DialogTitle>
          </DialogHeader>
          <div className="w-full bg-red-500/10 border border-red-500/20 rounded-xl p-4">
            <p className="text-center font-semibold text-xl text-purple-300">
              <span className="text-red-500">{payDialogTitle}</span>
            </p>
          </div>
          <PricingSection needTitle={false} className="leading-none" />
        </DialogContent>
      </Dialog>
    </div>
  )
}
