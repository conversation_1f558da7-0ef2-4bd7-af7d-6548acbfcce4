import { getTranslations } from 'next-intl/server'
import { Link } from '@i18n/routing'

export default async function MoreFeatures() {
  const t = await getTranslations()

  const features = [
    t('imageTools.tool1'),
    t('imageTools.tool2'),
    t('imageTools.tool3'),
    t('imageTools.tool4'),
    t('imageTools.tool5'),
    t('imageTools.tool6'),
    t('imageTools.tool7'),
    t('imageTools.tool8'),
    t('imageTools.tool9'),
    t('imageTools.tool10'),
    t('imageTools.tool11'),
    t('imageTools.tool12'),
    t('imageTools.tool13'),
    t('imageTools.tool14'),
    t('imageTools.tool15'),
    t('imageTools.tool16'),
    t('imageTools.tool17'),
    t('imageTools.tool18'),
    t('imageTools.tool19'),
    t('imageTools.tool20'),
  ]

  return (
    <section className="w-full bg-[#181028] py-16">
      <div className="container mx-auto px-4">
        {/* 标题和副标题 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-4">
            {t('imageTools.exploreMore')}
          </h2>
          <p className="text-lg md:text-xl text-gray-300 max-w-4xl mx-auto">
            {t('imageTools.chooseTools')}
          </p>
        </div>

        {/* 功能标签网格 */}
        <div className="flex flex-wrap justify-center gap-3 md:gap-4 max-w-6xl mx-auto">
          {features.map((feature, index) => (
            <button
              key={index}
              className="px-4 py-2 md:px-6 md:py-3 rounded-full bg-[#1a1a2e] border border-[#2d2d44] hover:border-[#4a4a6a] hover:bg-[#2a2a3e] transition-all duration-300 text-sm md:text-base text-gray-300 hover:text-white"
            >
              {feature}
            </button>
          ))}
        </div>

        {/* 底部CTA */}
        <div className="text-center mt-12">
          <Link
            href="/templates"
            className="inline-block bg-gradient-to-r from-fuchsia-600 via-purple-500 to-indigo-500 text-white px-8 py-3 rounded-lg shadow-lg hover:scale-105 transition-all font-semibold text-lg drop-shadow-[0_2px_16px_rgba(236,72,255,0.5)]"
          >
            {t('imageTools.exploreAllTools')}
          </Link>
        </div>
      </div>
    </section>
  )
}
