import { FC } from 'react'
import { useTranslations } from 'next-intl'

const TestimonialSection: FC = () => {
  // 使用根级别翻译对象，与PricingSection保持一致
  const t = useTranslations()

  const testimonials = [
    {
      name: '<PERSON>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Jess',
      role: t('TestimonialSection.testimonials.0.role'),
      content: t('TestimonialSection.testimonials.0.content'),
    },
    {
      name: '<PERSON>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Kevin',
      role: t('TestimonialSection.testimonials.1.role'),
      content: t('TestimonialSection.testimonials.1.content'),
    },
    {
      name: '<PERSON>.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Maria',
      role: t('TestimonialSection.testimonials.2.role'),
      content: t('TestimonialSection.testimonials.2.content'),
    },
    {
      name: '<PERSON>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Liam',
      role: t('TestimonialSection.testimonials.3.role'),
      content: t('TestimonialSection.testimonials.3.content'),
    },
    {
      name: 'Alyssa W.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Alyssa',
      role: t('TestimonialSection.testimonials.4.role'),
      content: t('TestimonialSection.testimonials.4.content'),
    },
    {
      name: 'Brian C.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Brian',
      role: t('TestimonialSection.testimonials.5.role'),
      content: t('TestimonialSection.testimonials.5.content'),
    },
    {
      name: 'Hannah S.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Hannah',
      role: t('TestimonialSection.testimonials.6.role'),
      content: t('TestimonialSection.testimonials.6.content'),
    },
    {
      name: 'Tyler N.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Tyler',
      role: t('TestimonialSection.testimonials.7.role'),
      content: t('TestimonialSection.testimonials.7.content'),
    },
    {
      name: 'Zoe D.',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Zoe',
      role: t('TestimonialSection.testimonials.8.role'),
      content: t('TestimonialSection.testimonials.8.content'),
    },
  ]

  return (
    <section className="relative py-16 overflow-hidden bg-[#181028]">
      <div className="max-w-6xl mx-auto ">
        <div className="pt-10 px-4 mb-10">
          <h2 className="text-3xl md:text-4xl font-bold text-indigo-400 drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)] mb-4 text-center">
            {t('TestimonialSection.title')}
          </h2>
          <p className="text-lg md:text-xl text-gray-300 max-w-4xl mx-auto text-center">
            {t('TestimonialSection.description')}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials.map((testimonial) => (
            <div
              key={testimonial.name}
              className="p-6 bg-gradient-to-br from-[#1a1333cc] via-[#181028cc] to-[#23203aee] rounded-2xl border-2 border-transparent hover:border-2 hover:border-fuchsia-500/60 transition-all shadow-md hover:shadow-[0_4px_32px_0_rgba(168,85,247,0.25)] backdrop-blur-md"
            >
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  {/* 头像区域 */}
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full overflow-hidden bg-gradient-to-r from-fuchsia-500 via-indigo-500 to-blue-500 p-[2px]">
                      <div className="w-full h-full rounded-full overflow-hidden bg-[#181028]">
                        <img
                          src={testimonial.avatar}
                          alt={testimonial.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    </div>
                    {/* 在线状态指示器 */}
                    <div className="absolute bottom-0 right-0 w-3 h-3 rounded-full bg-green-400 border-2 border-[#181028]"></div>
                  </div>
                  {/* 用户信息 */}
                  <div>
                    <div className="font-semibold text-white">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-indigo-300">
                      {testimonial.role}
                    </div>
                  </div>
                </div>

                {/* 评价内容 */}
                <p className="text-gray-200">{testimonial.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialSection
