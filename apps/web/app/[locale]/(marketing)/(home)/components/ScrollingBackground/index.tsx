'use client'

import React, { useState, useEffect } from 'react'

interface ScrollingBackgroundProps {
  images: string[]
  numColumns?: number
  animationDuration?: string
  rotationAngle?: number
  backgroundOpacity?: number
  children?: React.ReactNode
}

const ImageColumn = ({
  images,
  className = '',
  animationDuration = '60s',
  reverse = false,
}: {
  images: string[]
  className?: string
  animationDuration?: string
  reverse?: boolean
}) => (
  <div className={`flex flex-col gap-2 sm:gap-4 ${className}`}>
    {[...images, ...images].map((src, index) => (
      <img
        key={reverse ? `r-${index}` : `n-${index}`}
        src={src}
        alt={`滚动图片 ${index + 1}`}
        className="w-full h-auto object-cover rounded-lg shadow-sm sm:shadow-md"
      />
    ))}
  </div>
)

const ScrollingBackground: React.FC<ScrollingBackgroundProps> = ({
  images,
  numColumns = 4,
  animationDuration = '60s',
  rotationAngle = -15,
  backgroundOpacity = 0.3,
  children,
}) => {
  // 根据屏幕宽度调整列数
  const [columnCount, setColumnCount] = useState(numColumns)
  const [screenWidth, setScreenWidth] = useState(0) // 初始值为0，避免服务器端渲染问题

  useEffect(() => {
    // 设置初始屏幕宽度
    setScreenWidth(window.innerWidth)

    const updateDimensions = () => {
      const width = window.innerWidth
      setScreenWidth(width)

      if (width < 640) {
        // sm breakpoint
        setColumnCount(3)
      } else if (width < 1024) {
        // lg breakpoint
        setColumnCount(4)
      } else {
        setColumnCount(numColumns)
      }
    }

    updateDimensions() // 初始化
    window.addEventListener('resize', updateDimensions)
    return () => window.removeEventListener('resize', updateDimensions)
  }, [numColumns])

  // 将图片分配到不同的列中
  const columns = Array.from({ length: columnCount }, (_, i) =>
    images.filter((_, index) => index % columnCount === i)
  )

  // 为滚动的列创建唯一的动画名称
  const animationName = `scroll-${Math.random().toString(36).substring(7)}`
  const reverseAnimationName = `reverse-scroll-${Math.random()
    .toString(36)
    .substring(7)}`

  // 计算基于屏幕宽度的缩放比例
  const scaleValue = screenWidth < 640 ? 2 : 1.5

  return (
    <div className="relative w-full h-full overflow-hidden bg-gray-100">
      <style>
        {`
          @keyframes ${animationName} {
            0% { transform: translateY(0); }
            100% { transform: translateY(-50%); }
          }
          @keyframes ${reverseAnimationName} {
            0% { transform: translateY(-50%); }
            100% { transform: translateY(0); }
          }
          .animate-scroll {
            animation: ${animationName} ${animationDuration} linear infinite;
          }
          .animate-reverse-scroll {
            animation: ${reverseAnimationName} ${animationDuration} linear infinite;
          }
          @media (max-width: 640px) {
            .animate-scroll, .animate-reverse-scroll {
              animation-duration: ${
                parseInt(animationDuration as string) * 0.7
              }s;
            }
          }
        `}
      </style>

      <div
        className="absolute inset-0 z-0 overflow-hidden"
        style={{ opacity: backgroundOpacity }}
      >
        <div
          className="absolute inset-[-25%] flex justify-center items-center"
          style={{
            transform: `rotate(${rotationAngle}deg) scale(${scaleValue})`,
          }}
        >
          <div className="flex w-full h-full gap-1 sm:gap-4 px-1 sm:px-4">
            {columns.map((columnImages, i) => (
              <ImageColumn
                key={i}
                images={columnImages}
                className={`w-1/${columnCount} ${
                  i % 2 === 0 ? 'animate-scroll' : 'animate-reverse-scroll'
                }`}
                animationDuration={
                  screenWidth < 640
                    ? `${parseInt(animationDuration as string) * 0.7}s`
                    : animationDuration
                }
                reverse={i % 2 !== 0}
              />
            ))}
          </div>
        </div>
      </div>

      <div className="relative z-10 flex items-center justify-center w-full h-full">
        {children}
      </div>
    </div>
  )
}

export default ScrollingBackground
