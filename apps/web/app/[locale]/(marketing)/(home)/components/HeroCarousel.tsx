'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { ChevronLeft, ChevronRight } from 'lucide-react'

const HeroCarousel = () => {
  const [currentIndex, setCurrentIndex] = useState(0)

  // 轮播图片数据 - 对应 Discover popular features 的6个案例
  const carouselImages = [
    {
      src: '/images/home/<USER>',
      alt: 'AI Clothes Changer Demo',
      title: 'Virtual Try-On',
    },
    {
      src: '/images/home/<USER>',
      alt: 'Ghibli Style Demo',
      title: 'Ghibli Style',
    },
    {
      src: '/gif/iShot_2025-07-04_21.58.26.gif',
      alt: 'Photo Restoration Demo',
      title: 'Old Photo Restoration',
    },
    {
      src: '/images/home/<USER>',
      alt: 'Text to Image Demo',
      title: 'AI Text to Image',
    },
    {
      src: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/w4.mp4',
      alt: 'AI Hug Video Demo',
      title: 'AI Hug Video',
    },
    {
      src: 'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/videosource/w6.jpg',
      alt: 'Face Swap Demo',
      title: 'Fun Face Swap',
    },
  ]

  // 自动轮播
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) =>
        prevIndex === carouselImages.length - 1 ? 0 : prevIndex + 1
      )
    }, 6000) // 4秒切换一次

    return () => clearInterval(interval)
  }, [carouselImages.length])

  const goToPrevious = () => {
    setCurrentIndex(
      currentIndex === 0 ? carouselImages.length - 1 : currentIndex - 1
    )
  }

  const goToNext = () => {
    setCurrentIndex(
      currentIndex === carouselImages.length - 1 ? 0 : currentIndex + 1
    )
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
  }

  return (
    <div className="relative w-full h-[240px] sm:h-[280px] md:h-[320px] flex items-center justify-center md:justify-end group">
      {/* 主轮播容器 */}
      <div className="relative shadow-2xl rounded-xl overflow-hidden border-4 border-white/10 w-full h-full">
        {/* 图片容器 */}
        <div className="relative w-full h-full overflow-hidden">
          {carouselImages.map((image, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-all duration-700 ease-in-out ${
                index === currentIndex
                  ? 'opacity-100 transform translate-x-0'
                  : index < currentIndex
                  ? 'opacity-0 transform -translate-x-full'
                  : 'opacity-0 transform translate-x-full'
              }`}
            >
              {image.src.endsWith('.mp4') ? (
                <video
                  src={image.src}
                  autoPlay
                  loop
                  muted
                  playsInline
                  className="object-cover w-full h-full"
                />
              ) : (
                <>
                  <Image
                    src={image.src}
                    alt={image.alt}
                    width={600} // 增加图片宽度
                    height={300} // 减小图片高度
                    className="object-contain z-[1] absolute inset-0 w-full h-full"
                    priority={index === 0}
                  />
                  <Image
                    src={image.src}
                    alt={image.alt}
                    width={600} // 增加图片宽度
                    height={300} // 减小图片高度
                    className="object-cover absolute blur-sm inset-0 z-0 w-full h-full"
                    priority={index === 0}
                  />
                </>
              )}

              {/* 图片标题覆盖层 */}
              <div className="absolute inset-0 z-[2] bg-gradient-to-t from-black/30 via-transparent to-transparent">
                <div className="absolute bottom-4 left-4">
                  <h3 className="text-white text-sm font-medium drop-shadow-lg">
                    {image.title}
                  </h3>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 左右切换按钮 */}
        <button
          onClick={goToPrevious}
          className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 backdrop-blur-sm"
          aria-label="Previous image"
        >
          <ChevronLeft className="w-4 h-4" />
        </button>

        <button
          onClick={goToNext}
          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/20 hover:bg-black/40 text-white p-2 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 backdrop-blur-sm"
          aria-label="Next image"
        >
          <ChevronRight className="w-4 h-4" />
        </button>

        {/* 底部指示点 */}
        <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {carouselImages.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-white scale-125'
                  : 'bg-white/50 hover:bg-white/75'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

export default HeroCarousel
