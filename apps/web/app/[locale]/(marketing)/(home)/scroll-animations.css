/* 滚动动画增强样式 */
.scroll-animation-container {
  will-change: opacity, transform;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 动画性能优化 */
.scroll-animation-container * {
  will-change: auto;
}

/* 自定义缓动函数 */
.scroll-ease-bounce {
  transition-timing-function: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.scroll-ease-elastic {
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.scroll-ease-smooth {
  transition-timing-function: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 视差滚动效果 */
.parallax-container {
  transform-style: preserve-3d;
  perspective: 1000px;
}

.parallax-element {
  transform: translateZ(0);
  will-change: transform;
}

/* 交错动画 */
.stagger-animation > * {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.stagger-animation.animate > * {
  opacity: 1;
  transform: translateY(0);
}

.stagger-animation.animate > *:nth-child(1) {
  transition-delay: 0.1s;
}
.stagger-animation.animate > *:nth-child(2) {
  transition-delay: 0.2s;
}
.stagger-animation.animate > *:nth-child(3) {
  transition-delay: 0.3s;
}
.stagger-animation.animate > *:nth-child(4) {
  transition-delay: 0.4s;
}
.stagger-animation.animate > *:nth-child(5) {
  transition-delay: 0.5s;
}
.stagger-animation.animate > *:nth-child(6) {
  transition-delay: 0.6s;
}

/* 淡入效果 */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 缩放效果 */
.scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scale-in.visible {
  opacity: 1;
  transform: scale(1);
}

/* 滑动效果 */
.slide-up {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-up.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-down {
  opacity: 0;
  transform: translateY(-50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-down.visible {
  opacity: 1;
  transform: translateY(0);
}

.slide-left {
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-left.visible {
  opacity: 1;
  transform: translateX(0);
}

.slide-right {
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.slide-right.visible {
  opacity: 1;
  transform: translateX(0);
}

/* 旋转效果 */
.rotate-in {
  opacity: 0;
  transform: rotate(-10deg) scale(0.8);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.rotate-in.visible {
  opacity: 1;
  transform: rotate(0deg) scale(1);
}

/* 响应式动画调整 */
@media (max-width: 768px) {
  .slide-up,
  .slide-down {
    transform: translateY(30px);
  }

  .slide-left,
  .slide-right {
    transform: translateX(30px);
  }

  .stagger-animation.animate > * {
    transition-delay: 0.05s !important;
  }
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .scroll-animation-container,
  .fade-in,
  .scale-in,
  .slide-up,
  .slide-down,
  .slide-left,
  .slide-right,
  .rotate-in {
    transition: none !important;
    animation: none !important;
  }

  .stagger-animation > * {
    transition: none !important;
  }
}
