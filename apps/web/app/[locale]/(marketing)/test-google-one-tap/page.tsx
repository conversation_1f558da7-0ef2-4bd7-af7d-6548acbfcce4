'use client'

import { useEffect, useState } from 'react'
import { getGoogleOneTapConfig, isGoogleOneTapSupported } from '@/utils/google-one-tap-config'
import { getUserFromClientCookies } from '@/utils/client-cookies'

export default function TestGoogleOneTapPage() {
  const [config, setConfig] = useState<any>(null)
  const [isSupported, setIsSupported] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const [clientId, setClientId] = useState<string>('')

  useEffect(() => {
    // 检查环境变量
    const envClientId = process.env.NEXT_PUBLIC_CLIENT_ID
    setClientId(envClientId || 'Not configured')

    // 检查配置
    const oneTapConfig = getGoogleOneTapConfig()
    setConfig(oneTapConfig)

    // 检查是否支持
    const supported = isGoogleOneTapSupported()
    setIsSupported(supported)

    // 检查用户信息
    const user = getUserFromClientCookies()
    setUserInfo(user)

    console.log('=== Google One Tap Debug Info ===')
    console.log('NEXT_PUBLIC_CLIENT_ID:', envClientId)
    console.log('Config:', oneTapConfig)
    console.log('Is Supported:', supported)
    console.log('User Info:', user)
    console.log('================================')
  }, [])

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-8">Google One Tap Test Page</h1>
      
      <div className="space-y-6">
        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Environment Variables</h2>
          <p><strong>NEXT_PUBLIC_CLIENT_ID:</strong> {clientId}</p>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Configuration</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(config, null, 2)}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Environment Support</h2>
          <p><strong>Is Supported:</strong> {isSupported ? 'Yes' : 'No'}</p>
          <p><strong>Hostname:</strong> {typeof window !== 'undefined' ? window.location.hostname : 'N/A'}</p>
          <p><strong>Protocol:</strong> {typeof window !== 'undefined' ? window.location.protocol : 'N/A'}</p>
          <p><strong>Port:</strong> {typeof window !== 'undefined' ? window.location.port : 'N/A'}</p>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">User Information</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(userInfo, null, 2)}
          </pre>
        </div>

        <div className="bg-blue-100 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Instructions</h2>
          <p>If Google One Tap is not showing:</p>
          <ul className="list-disc list-inside mt-2 space-y-1">
            <li>Make sure NEXT_PUBLIC_CLIENT_ID is configured</li>
            <li>Check that you're not already logged in</li>
            <li>Ensure you're not using a proxy server</li>
            <li>Verify the domain is configured in Google Console</li>
            <li>Check browser console for any errors</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
