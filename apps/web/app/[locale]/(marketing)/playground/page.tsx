import { ImageEditor } from '../../../../components/ImageEditor'

export async function generateMetadata() {
  return {
    title: 'Playground - Remove Background',
    description: 'Edit and process your uploaded images',
  }
}

const PlaygroundPage = async () => {
  return (
    <div className="min-h-screen bg-gray-100 overflow-hidden">
      {/* Main Layout */}
      <div className="flex h-full">
        {/* Left Side - Main Canvas Area */}
        <div className="flex-1 flex flex-col">
          <ImageEditor />
        </div>
      </div>
    </div>
  )
}

export default PlaygroundPage
