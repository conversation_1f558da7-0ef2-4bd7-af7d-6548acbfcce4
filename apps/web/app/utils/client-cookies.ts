// utils/client-cookies.ts
import Cookies from 'js-cookie'

export function getUserFromClientCookies() {
  const avatar = Cookies.get('oauth_avatar')
  const email = Cookies.get('oauth_email')
  const id = Cookies.get('oauth_id')
  const points = Cookies.get('points')
  const userId = Cookies.get('userId')
  const membershipStatus = Cookies.get('membershipStatus')

  if (!avatar || !email || !id) {
    return null
  }

  return {
    avatar,
    email,
    id,
    userId,
    membershipStatus,
    points,
    balance: 0,
  }
}
