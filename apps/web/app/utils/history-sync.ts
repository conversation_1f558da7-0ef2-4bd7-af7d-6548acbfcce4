import { supabase } from '../lib/supabaseClient'
import { getUserFromServerCookies } from './server-cookies'
import { cookies } from 'next/headers'

// 历史记录类型
export type HistoryRecordType = 'image' | 'video'

// 状态映射：KieAI任务状态 → 本地状态
export function mapKieAITaskStateToLocal(kieaiTaskState: string): string {
  switch (kieaiTaskState) {
    case 'SUCCESS':
      return 'SUCCESS'
    case 'FAILED':
    case 'GENERATE_FAILED':
      return 'FAILED'
    case 'PROCESSING':
    case 'QUEUED':
    case 'RUNNING':
      return 'PROCESSING'
    default:
      return 'PENDING'
  }
}

// 查找历史记录
export async function findHistoryRecord(
  taskId: string,
  recordType: HistoryRecordType
): Promise<any> {
  try {
    // const taskTypes = getTaskTypeFilter(recordType)

    const { data, error } = await supabase
      .from('removeai_history')
      .select('*')
      .eq('external_task_id', taskId)
      // .in('task_type', taskTypes)
      .is('deleted_at', null)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw error
    }

    return data || null
  } catch (error) {
    console.error(`Error finding ${recordType} history record:`, error)
    return null
  }
}

// 更新历史记录
export async function updateHistoryRecord(
  taskId: string,
  status: string,
  resultData: any,
  recordType: HistoryRecordType,
  errorMessage?: string
): Promise<any> {
  try {
    console.log(`Updating ${recordType} history record:`, {
      taskId,
      status,
      hasResultData: !!resultData,
      errorMessage,
    })

    // 查找现有记录
    const existingRecord = await findHistoryRecord(taskId, recordType)

    if (!existingRecord) {
      console.warn(
        `No existing history record found for ${recordType} task_id:`,
        taskId
      )
      return null
    }

    // 构建更新数据
    const updateData: any = {
      status: status,
      updated_at: new Date().toISOString(),
    }

    // 更新结果数据
    if (resultData) {
      updateData.result_data = {
        ...existingRecord.result_data,
        taskId: taskId,
        status: status,
        ...resultData,
      }
    }
    console.log('updateData', updateData)
    // 更新元数据
    if (existingRecord.metadata) {
      updateData.metadata = {
        ...existingRecord.metadata,
        updated_at: new Date().toISOString(),
        last_status: status,
      }
    }

    // 如果任务完成或失败，设置完成时间
    if (status === 'SUCCESS' || status === 'FAILED') {
      updateData.completed_at = new Date().toISOString()
    }

    // 如果有错误信息，设置error_message字段
    if (errorMessage) {
      updateData.error_message = errorMessage
    }

    // 更新记录
    const { data, error } = await supabase
      .from('removeai_history')
      .update(updateData)
      .eq('id', existingRecord.id)
      .select()

    if (error) {
      console.error(`Failed to update ${recordType} history record:`, error)
      throw error
    }

    console.log(
      `${recordType} history record updated successfully:`,
      data?.[0]?.id
    )
    return data?.[0]
  } catch (error) {
    console.error(`Error updating ${recordType} history record:`, error)
    throw error
  }
}

// 主要的异步历史记录同步函数
export async function syncHistoryWithKieAI(
  taskId: string,
  kieaiData: any,
  recordType: HistoryRecordType = 'image'
): Promise<void> {
  // 获取用户信息（可选，失败不影响主流程）
  let user = null
  try {
    const cookieStore = await cookies()
    user = await getUserFromServerCookies(cookieStore)
  } catch (error) {
    console.log(
      'User authentication failed in history sync, skipping user-specific operations'
    )
    return // 没有用户信息就不执行同步
  }

  // 只有在有用户信息时才执行历史记录同步
  if (!user) {
    return
  }

  // 异步执行状态同步逻辑，不阻塞主流程
  setImmediate(async () => {
    try {
      console.log(`Starting ${recordType} history sync for task:`, taskId)

      // 查找本地历史记录
      const localRecord = await findHistoryRecord(taskId, recordType)

      if (!localRecord) {
        console.log(
          `No local ${recordType} history record found for task:`,
          taskId
        )
        return
      }

      // 根据KieAI响应确定最新状态
      let latestStatus: string
      let resultData: any = null
      let errorMessage: string | undefined

      if (kieaiData.code === 200) {
        // KieAI返回成功，检查任务状态
        if (kieaiData.data?.status) {
          latestStatus = mapKieAITaskStateToLocal(kieaiData.data.status)
        } else {
          latestStatus = 'PENDING'
        }
        console.log('kieaiData', kieaiData)
        // 如果有结果数据，保存到resultData
        if (kieaiData.data) {
          resultData = {
            raw_output: kieaiData.data,
            imageUrls: kieaiData.data.response?.resultUrls,
            generatedImageUrls: kieaiData.data.response?.resultUrls,
            originalImageUrls: kieaiData.data.response?.originalImageUrls || [],
            task_id: taskId,
            generated_at: new Date().toISOString(),
          }
        }
      } else {
        // KieAI返回失败
        latestStatus = 'FAILED'
        errorMessage = kieaiData.msg || `KieAI API error: ${kieaiData.code}`
        resultData = {
          error_code: kieaiData.code,
          error_message: errorMessage,
          failed_at: new Date().toISOString(),
        }
      }

      // 检查是否需要更新
      if (localRecord.status === latestStatus) {
        console.log(
          `${recordType} record status already up to date:`,
          latestStatus
        )
        console.log('localRecord', localRecord.status, latestStatus)
        return
      }

      // 更新历史记录
      await updateHistoryRecord(
        taskId,
        latestStatus,
        resultData,
        recordType,
        errorMessage
      )

      console.log(`${recordType} history sync completed for task:`, taskId)
    } catch (error) {
      console.error(
        `Error in ${recordType} history sync for task ${taskId}:`,
        error
      )
      // 错误不会影响主流程，只记录日志
    }
  })
}

// 便捷的调用函数
export const syncImageHistoryWithKieAI = (taskId: string, kieaiData: any) =>
  syncHistoryWithKieAI(taskId, kieaiData, 'image')

export const syncVideoHistoryWithKieAI = (taskId: string, kieaiData: any) =>
  syncHistoryWithKieAI(taskId, kieaiData, 'video')
