import Cookies from 'js-cookie'
interface UserInfo {
  created_at: string
  username: string
  avatar_url: string
  email: string
  last_login_time: string
  points: number
  membership_status: string
  membership_level: string
  membership_start_date: string | null
  membership_end_date: string | null
  updated_at: string
  is_membership_active: boolean
  membership_days_left: number
}

export interface UserCookie {
  userId: string
  username: string
  userEmail: string
  userCreatedAt: string
  updatedAt: string
  points: string
  membershipEndDate: string
  membershipLevel: string
  membershipStatus: string
  avatarUrl: string
}

export const getUserInfoFromCookie = (): UserCookie => {
  return {
    userId: Cookies.get('userId') || '',
    username: Cookies.get('username') || '',
    userEmail: Cookies.get('userEmail') || '',
    userCreatedAt: Cookies.get('userCreatedAt') || '',
    updatedAt: Cookies.get('updatedAt') || '',
    points: Cookies.get('points') || '0',
    membershipEndDate: Cookies.get('membershipEndDate') || '',
    membershipLevel: Cookies.get('membershipLevel') || '',
    membershipStatus: Cookies.get('membershipStatus') || '',
    avatarUrl: Cookies.get('avatarUrl') || '',
  }
}

export const isUserLoggedIn = (): boolean => {
  const oauthEmail = Cookies.get('oauth_email')
  const userId = Cookies.get('userId')
  const username = Cookies.get('username')

  if (oauthEmail && userId && username) {
    return true
  }
  return false
}

export const getUserId = (): string => {
  const value = localStorage.getItem('email')

  if (value) {
    return value
  }

  // 默认返回值
  return '413564'
}

export const getUserIdFromCookie = (): string => {
  const value = Cookies.get('userId')
  if (value) {
    return value
  }
  return ''
}

export const saveObj = (obj: any) => {
  Object.entries(obj).forEach(([key, value]) => {
    // 如果是字符串，直接存储
    if (typeof value === 'string') {
      localStorage.setItem(key, value)
    } else {
      localStorage.setItem(key, JSON.stringify(value))
    }
  })
}

export function detectMobileDevice() {
  // 1. 通过媒体查询检测屏幕尺寸
  const isMobileByViewport = window.matchMedia('(max-width: 767px)').matches

  // 2. 通过触摸能力检测
  const hasTouchCapability =
    'ontouchstart' in window || navigator.maxTouchPoints > 0

  // 3. 通过用户代理检测 (作为补充)
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android',
    'iphone',
    'ipod',
    'opera mini',
    'windows phone',
  ]
  const isMobileByUA = mobileKeywords.some((keyword) =>
    userAgent.includes(keyword)
  )

  // 4. 设备方向API检测 (移动设备特性)
  const hasOrientationApi = Boolean(window.DeviceOrientationEvent)

  // 5. 屏幕比例检测 (移动设备通常是窄而高)
  const screenRatio = window.innerHeight / window.innerWidth
  const isPortraitRatio = screenRatio > 1.2

  // 综合判断 - 可以根据需要调整权重
  let mobileProbability = 0
  if (isMobileByViewport) mobileProbability += 0.4
  if (hasTouchCapability) mobileProbability += 0.3
  if (isMobileByUA) mobileProbability += 0.2
  if (hasOrientationApi && isPortraitRatio) mobileProbability += 0.1

  // 结果详情
  return {
    isMobile: mobileProbability >= 0.5,
    confidence: mobileProbability,
    details: {
      viewport: isMobileByViewport,
      touchCapable: hasTouchCapability,
      userAgent: isMobileByUA,
      orientation: hasOrientationApi,
      screenRatio: screenRatio.toFixed(2),
    },
  }
}

interface MenuItem {
  title: string
  desc: string
  href: string
  seoHref: string
  isHot?: boolean
  isNew?: boolean
  coming?: boolean
  point: number
}

interface MenuCategory {
  label: string
  bgClass: string
  items: MenuItem[]
}

interface MenuCategories {
  [key: string]: MenuCategory
}


