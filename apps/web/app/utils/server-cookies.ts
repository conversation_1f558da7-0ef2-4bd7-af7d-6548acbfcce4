import { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies'

interface User {
  id: string | number
  email: string
}

export const getUserFromServerCookies = async (
  cookieStore: ReadonlyRequestCookies
): Promise<User | null> => {
  try {
    const email = cookieStore.get('userEmail')?.value
    const id = cookieStore.get('userId')?.value
    if (!email || !id) {
      return null
    }
    return { email, id }
  } catch (error) {
    console.error('Error parsing user cookie:', error)
    return null
  }
}
