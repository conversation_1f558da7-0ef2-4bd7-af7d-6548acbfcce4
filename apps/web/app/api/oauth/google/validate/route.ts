import { Google } from 'arctic'
import { NextRequest } from 'next/server'

const CALLBACK_URL = 'http://localhost:3000/api/oauth/google/callback'
// const CALLBACK_URL = process.env.NEXT_PUBLIC_GOOGLE_CALLBACK_URL || 'http://localhost:3000/api/auth/google/callback'

export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json()
    const { code, verifier } = body

    // 验证必需参数
    if (!code || !verifier) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }

    // 初始化 Google Auth
    const googleAuth = new Google(
      process.env.GOOGLE_CLIENT_ID!,
      process.env.GOOGLE_CLIENT_SECRET!,
      CALLBACK_URL
    )

    // 验证授权码
    const tokens = await googleAuth.validateAuthorizationCode(code, verifier)

    const googleUserResponse = await fetch(
      'https://openidconnect.googleapis.com/v1/userinfo',
      {
        headers: {
          Authorization: `Bearer ${tokens.accessToken()}`,
        },
      }
    )

    const googleUser = await googleUserResponse.json()

    // 返回成功响应
    return new Response(JSON.stringify({ googleUser }), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Google auth validation error:', error)

    // 返回错误响应
    return new Response(
      JSON.stringify({
        error: 'Authentication failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}
