// apps/web/app/api/webhooks/payment/route.ts
import { NextResponse } from 'next/server'
import { supabase } from '../../../lib/supabaseClient'
import crypto from 'crypto'
import { sub } from 'date-fns'

// 为了部署随便写一行12345678910

// 定义webhook类型枚举
enum WebhookType {
  OrderCharged = 1,
  OrderRefunded = 2,
  OrderChargedBack = 3,
  OrderDeclined = 4,
  OrderPartiallyRefunded = 5,
  SubscriptionChargeSucceed = 6,
  SubscriptionChargeFailed = 7,
  SubscriptionSuspended = 8,
  SubscriptionRenewed = 9,
  SubscriptionTerminated = 10,
  SubscriptionFinished = 11,
  LicenseRequested = 12,
  // ... 其他类型 ...
}

// 处理订单支付成功
async function handleOrderCharged(data: any) {
  try {
    // 解析 CHECKOUT_QUERY_STRING 获取内部订单ID
    const queryParams = new URLSearchParams(data.CHECKOUT_QUERY_STRING)
    const internalOrderId = queryParams.get('internal_order_id')
    const membershipLevelId = queryParams.get('membership_level_id')

    if (!internalOrderId) {
      throw new Error('Internal order ID not found in callback data')
    }

    // 1. 更新订单状态
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .update({
        status: 'paid',
        payment_method: data.PAYMENT_METHOD_NAME,
        subscription_id: data.SUBSCRIPTION_ID,
        paypro_order_id: data.ORDER_ID,
        updated_at: new Date().toISOString(),
      })
      .eq('id', internalOrderId)
      .select()
      .single()

    if (orderError || !order) {
      throw new Error(`Order update failed: ${orderError?.message}`)
    }

    // 获取会员等级信息
    const { data: membershipLevel, error: membershipError } = await supabase
      .from('removeai_membership_level')
      .select('*')
      .eq('id', membershipLevelId)
      .single()

    if (membershipError || !membershipLevel) {
      throw new Error(`Membership level not found: ${membershipError?.message}`)
    }

    // 2. 记录支付信息
    await supabase.from('removeai_payment').insert({
      order_id: order.id,
      user_id: order.user_id,
      transaction_id: data.ORDER_ID,
      amount: membershipLevel.amount,
      currency: membershipLevel.currency,
      payment_method: data.PAYMENT_METHOD_NAME,
      status: 'success',
      ipn_type: data.IPN_TYPE_NAME,
      raw_data: data,
    })

    // 3. 更新用户会员状态
    const membershipEndDate = new Date()
    membershipEndDate.setDate(
      membershipEndDate.getDate() + membershipLevel.duration_days
    )

    await supabase
      .from('removeai_user')
      .update({
        membership_status: 'active',
        membership_level: membershipLevel.name,
        membership_start_date: new Date().toISOString(),
        membership_end_date: membershipEndDate.toISOString(),
        updated_at: new Date().toISOString(),
        points: membershipLevel.points,
      })
      .eq('id', order.user_id)

    // 4. 记录会员变更
    await supabase.from('removeai_membership_log').insert({
      user_id: order.user_id,
      order_id: order.id,
      old_status: 'free',
      new_status: 'active',
      old_level: 'free',
      new_level: membershipLevel.name,
      start_date: new Date().toISOString(),
      end_date: membershipEndDate.toISOString(),
      change_reason: 'purchase',
    })
  } catch (error) {
    console.error('Error handling order charged:', error)
    throw error
  }
}

// 处理订单退款
async function handleOrderRefunded(data: any) {
  try {
    const queryParams = new URLSearchParams(data.CHECKOUT_QUERY_STRING)
    const internalOrderId = queryParams.get('internal_order_id')

    if (!internalOrderId) {
      throw new Error('Internal order ID not found in callback data')
    }

    // 1. 更新订单状态
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .update({
        status: 'refunded',
        updated_at: new Date().toISOString(),
      })
      .eq('id', internalOrderId)
      .select()
      .single()

    if (orderError || !order) {
      throw new Error(`Order update failed: ${orderError?.message}`)
    }

    // 2. 记录退款支付信息
    await supabase.from('removeai_payment').insert({
      order_id: order.id,
      user_id: order.user_id,
      transaction_id: data.ORDER_ID,
      amount: data.REFUND_AMOUNT || data.ORDER_TOTAL_AMOUNT,
      currency: data.ORDER_CURRENCY_CODE,
      payment_method: data.PAYMENT_METHOD_NAME,
      status: 'refunded',
      ipn_type: data.IPN_TYPE_NAME,
      raw_data: data,
    })

    // 3. 更新用户会员状态
    await supabase
      .from('removeai_user')
      .update({
        membership_status: 'expired',
        membership_level: 'free',
        membership_end_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', order.user_id)

    // 4. 记录会员变更
    await supabase.from('removeai_membership_log').insert({
      user_id: order.user_id,
      order_id: order.id,
      old_status: 'active',
      new_status: 'expired',
      old_level: order.membership_level,
      new_level: 'free',
      end_date: new Date().toISOString(),
      change_reason: 'refund',
    })
  } catch (error) {
    console.error('Error handling order refunded:', error)
    throw error
  }
}

// 处理订单支付失败
async function handleOrderDeclined(data: any) {
  try {
    const queryParams = new URLSearchParams(data.CHECKOUT_QUERY_STRING)
    const internalOrderId = queryParams.get('internal_order_id')

    if (!internalOrderId) {
      throw new Error('Internal order ID not found in callback data')
    }

    // 1. 更新订单状态
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .update({
        status: 'failed',
        error_message: data.ERROR_MESSAGE || 'Payment declined',
        updated_at: new Date().toISOString(),
      })
      .eq('id', internalOrderId)
      .select()
      .single()

    if (orderError || !order) {
      throw new Error(`Order update failed: ${orderError?.message}`)
    }

    // 2. 记录失败支付信息
    await supabase.from('removeai_payment').insert({
      order_id: order.id,
      user_id: order.user_id,
      transaction_id: data.ORDER_ID,
      amount: data.ORDER_TOTAL_AMOUNT,
      currency: data.ORDER_CURRENCY_CODE,
      payment_method: data.PAYMENT_METHOD_NAME,
      status: 'failed',
      error_message: data.ERROR_MESSAGE || 'Payment declined',
      ipn_type: data.IPN_TYPE_NAME,
      raw_data: data,
    })
  } catch (error) {
    console.error('Error handling order declined:', error)
    throw error
  }
}

// 处理订单拒付
async function handleOrderChargedBack(data: any) {
  try {
    const queryParams = new URLSearchParams(data.CHECKOUT_QUERY_STRING)
    const internalOrderId = queryParams.get('internal_order_id')

    if (!internalOrderId) {
      throw new Error('Internal order ID not found in callback data')
    }

    // 1. 更新订单状态
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .update({
        status: 'chargeback',
        error_message: data.CHARGEBACK_REASON || 'Chargeback initiated',
        updated_at: new Date().toISOString(),
      })
      .eq('id', internalOrderId)
      .select()
      .single()

    if (orderError || !order) {
      throw new Error(`Order update failed: ${orderError?.message}`)
    }

    // 2. 记录拒付支付信息
    await supabase.from('removeai_payment').insert({
      order_id: order.id,
      user_id: order.user_id,
      transaction_id: data.ORDER_ID,
      amount: data.CHARGEBACK_AMOUNT || data.ORDER_TOTAL_AMOUNT,
      currency: data.ORDER_CURRENCY_CODE,
      payment_method: data.PAYMENT_METHOD_NAME,
      status: 'chargeback',
      error_message: data.CHARGEBACK_REASON || 'Chargeback',
      ipn_type: data.IPN_TYPE_NAME,
      raw_data: data,
    })

    // 3. 更新用户会员状态
    await supabase
      .from('removeai_user')
      .update({
        membership_status: 'expired',
        membership_level: 'free',
        membership_end_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', order.user_id)

    // 4. 记录会员变更
    await supabase.from('removeai_membership_log').insert({
      user_id: order.user_id,
      order_id: order.id,
      old_status: 'active',
      new_status: 'expired',
      old_level: order.membership_level,
      new_level: 'free',
      end_date: new Date().toISOString(),
      change_reason: 'chargeback',
    })
  } catch (error) {
    console.error('Error handling chargeback:', error)
    throw error
  }
}

// 处理订单部分退款
async function handleOrderPartiallyRefunded(data: any) {
  try {
    // 1. 更新订单状态
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .update({
        status: 'partially_refunded',
        updated_at: new Date().toISOString(),
      })
      .eq('order_number', data.ORDER_ID)
      .select(
        `
        *,
        removeai_membership_level (*)
      `
      )
      .single()

    if (orderError || !order) {
      throw new Error(`Order update failed: ${orderError?.message}`)
    }

    // 2. 记录部分退款支付信息
    await supabase.from('removeai_payment').insert({
      order_id: order.id,
      user_id: order.user_id,
      transaction_id: data.ORDER_ID,
      amount: data.REFUND_AMOUNT,
      currency: order.currency,
      payment_method: data.PAYMENT_METHOD_NAME,
      status: 'partially_refunded',
      ipn_type: data.IPN_TYPE_NAME,
      raw_data: data,
    })

    // 3. 计算退款比例
    const refundRatio = Number(data.REFUND_AMOUNT) / Number(order.amount)

    // 4. 如果退款比例超过50%，更新会员状态
    if (refundRatio >= 0.5) {
      // 更新用户会员状态
      await supabase
        .from('removeai_user')
        .update({
          membership_status: 'expired',
          updated_at: new Date().toISOString(),
        })
        .eq('id', order.user_id)

      // 记录会员变更
      await supabase.from('removeai_membership_log').insert({
        user_id: order.user_id,
        order_id: order.id,
        old_status: 'active',
        new_status: 'expired',
        old_level: order.membership_level,
        new_level: 'free',
        change_reason: 'partial_refund',
      })
    }
  } catch (error) {
    console.error('Error handling partial refund:', error)
    throw error
  }
}

// 处理订阅续费成功
async function handleSubscriptionChargeSucceed(data: any) {
  try {
    // 1. 先查询订单和用户信息
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .select(
        `
        *,
        removeai_user (*)
      `
      )
      .eq('paypro_order_id', data.SUBSCRIPTION_INITIAL_ORDER_ID)
      .single()

    if (orderError || !order) {
      throw new Error(
        `Order not found for ORDER_ID ${data.SUBSCRIPTION_INITIAL_ORDER_ID}: ${orderError?.message}`
      )
    }

    // 2. 查询会员等级信息
    const { data: membershipLevel, error: membershipError } = await supabase
      .from('removeai_membership_level')
      .select('*')
      .eq('description', order.removeai_user.membership_level)
      .single()

    if (membershipError || !membershipLevel) {
      throw new Error(
        `Membership level not found for level ${order.removeai_user.membership_level}: ${membershipError?.message}`
      )
    }

    // 3. 记录支付信息
    await supabase.from('removeai_payment').insert({
      order_id: order.id,
      user_id: order.user_id,
      transaction_id: data.ORDER_ID,
      amount: data.ORDER_TOTAL_AMOUNT,
      currency: data.ORDER_CURRENCY_CODE,
      payment_method: data.PAYMENT_METHOD_NAME,
      status: 'success',
      ipn_type: data.IPN_TYPE_NAME,
      raw_data: data,
      payment_type: 'subscription',
    })

    // 4. 更新用户会员状态
    const currentEndDate = new Date()
    const newEndDate = new Date(currentEndDate)
    newEndDate.setDate(newEndDate.getDate() + membershipLevel.duration_days)

    await supabase
      .from('removeai_user')
      .update({
        membership_status: 'active',
        membership_level: membershipLevel.name,
        membership_end_date: newEndDate.toISOString(),
        updated_at: new Date().toISOString(),
        points: membershipLevel.points,
      })
      .eq('id', order.user_id)

    // 5. 记录会员变更
    await supabase.from('removeai_membership_log').insert({
      user_id: order.user_id,
      order_id: order.id,
      old_status: order.removeai_user.membership_status,
      new_status: 'active',
      old_level: membershipLevel.name,
      new_level: membershipLevel.name,
      start_date: currentEndDate.toISOString(),
      end_date: newEndDate.toISOString(),
      change_reason: 'subscription_renewal',
    })
  } catch (error) {
    console.error('Error handling subscription charge succeed:', error)
    throw error
  }
}

// 处理订阅续费失败
async function handleSubscriptionChargeFailed(data: any) {
  try {
    // 1. 通过 ORDER_ID 查询订单和用户信息
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .select(
        `
        *,
        removeai_user!user_id (*,
          removeai_membership_level!inner (name)
        )
      `
      )
      .eq('paypro_order_id', data.SUBSCRIPTION_INITIAL_ORDER_ID)
      .single()

    if (orderError || !order) {
      throw new Error(
        `Order not found for ORDER_ID ${data.SUBSCRIPTION_INITIAL_ORDER_ID}: ${orderError?.message}`
      )
    }

    // 2. 记录失败的支付信息
    await supabase.from('removeai_payment').insert({
      order_id: order.id,
      user_id: order.user_id,
      transaction_id: data.ORDER_ID,
      amount: data.ORDER_TOTAL_AMOUNT,
      currency: data.ORDER_CURRENCY_CODE,
      payment_method: data.PAYMENT_METHOD_NAME,
      status: 'failed',
      error_message: data.ERROR_MESSAGE || 'Subscription charge failed',
      ipn_type: data.IPN_TYPE_NAME,
      raw_data: data,
      payment_type: 'subscription',
    })

    // 3. 标记用户会员状态为待续费
    await supabase
      .from('removeai_user')
      .update({
        membership_status: 'pending_renewal',
        updated_at: new Date().toISOString(),
      })
      .eq('id', order.user_id)

    // 4. 记录会员变更
    await supabase.from('removeai_membership_log').insert({
      user_id: order.user_id,
      order_id: order.id,
      old_status: 'active',
      new_status: 'pending_renewal',
      old_level: order.removeai_user.removeai_membership_level.name,
      new_level: order.removeai_user.removeai_membership_level.name,
      change_reason: 'subscription_charge_failed',
    })
  } catch (error) {
    console.error('Error handling subscription charge failed:', error)
    throw error
  }
}

// 处理订阅暂停
async function handleSubscriptionSuspended(data: any) {
  try {
    // 1. 先查询订单和用户信息
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .select(
        `
        *,
        removeai_user (*)
      `
      )
      .eq('paypro_order_id', data.SUBSCRIPTION_INITIAL_ORDER_ID)
      .single()

    if (orderError || !order) {
      throw new Error(
        `Order not found for ORDER_ID ${data.SUBSCRIPTION_INITIAL_ORDER_ID}: ${orderError?.message}`
      )
    }

    // 2. 查询会员等级信息
    const { data: membershipLevel, error: membershipError } = await supabase
      .from('removeai_membership_level')
      .select('*')
      .eq('description', order.removeai_user.membership_level)
      .single()

    if (membershipError || !membershipLevel) {
      throw new Error(
        `Membership level not found for level ${order.removeai_user.membership_level}: ${membershipError?.message}`
      )
    }

    // 3. 更新用户会员状态
    await supabase
      .from('removeai_user')
      .update({
        membership_status: 'suspended',
        updated_at: new Date().toISOString(),
      })
      .eq('id', order.user_id)

    // 4. 记录会员变更
    await supabase.from('removeai_membership_log').insert({
      user_id: order.user_id,
      order_id: order.id,
      old_status: order.removeai_user.membership_status,
      new_status: 'suspended',
      old_level: membershipLevel.name,
      new_level: membershipLevel.name,
      change_reason: 'subscription_suspended',
    })
  } catch (error) {
    console.error('Error handling subscription suspended:', error)
    throw error
  }
}

// 处理订阅终止
async function handleSubscriptionTerminated(data: any) {
  try {
    // 1. 通过 ORDER_ID 查询订单和用户信息
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .select(
        `
        *,
        removeai_user!user_id (*,
          removeai_membership_level!inner (name)
        )
      `
      )
      .eq('paypro_order_id', data.SUBSCRIPTION_INITIAL_ORDER_ID)
      .single()

    if (orderError || !order) {
      throw new Error(
        `Order not found for ORDER_ID ${data.SUBSCRIPTION_INITIAL_ORDER_ID}: ${orderError?.message}`
      )
    }

    // 2. 更新用户会员状态
    await supabase
      .from('removeai_user')
      .update({
        membership_status: 'expired',
        membership_level: 'free',
        membership_end_date: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', order.user_id)

    // 3. 记录会员变更
    await supabase.from('removeai_membership_log').insert({
      user_id: order.user_id,
      order_id: order.id,
      old_status: 'active',
      new_status: 'expired',
      old_level: order.removeai_user.removeai_membership_level.name,
      new_level: 'free',
      end_date: new Date().toISOString(),
      change_reason: 'subscription_terminated',
    })
  } catch (error) {
    console.error('Error handling subscription terminated:', error)
    throw error
  }
}

// PayPro Global 允许的 IP 地址白名单
const ALLOWED_IPS = [
  '***************',
  '************',
  '2604:a880:400:d0::1843:7001',
  '2604:a880:400:d1::b6c:c001',
  '::1',
]

// 验证签名
const verifySignature = (
  data: any,
  signature: string,
  validationKey: string
) => {
  const signatureString = `${data.ORDER_ID}${data.ORDER_STATUS}${data.ORDER_TOTAL_AMOUNT}${data.CUSTOMER_EMAIL}${validationKey}${data.TEST_MODE}${data.IPN_TYPE_NAME}`
  const calculatedSignature = crypto
    .createHash('sha256')
    .update(signatureString)
    .digest('hex')
  return calculatedSignature === signature
}

export async function POST(req: Request) {
  try {
    // 获取原始数据
    const body = await req.formData()
    const data = Object.fromEntries(body)

    // 立即记录原始数据到 removeai_user_order 表
    const { error: orderLogError } = await supabase
      .from('removeai_user_order')
      .insert({
        create_time: new Date().toISOString(),
        content: JSON.stringify(data), // 存储完整的回调数据
        order_id: data.ORDER_ID,
        transaction_id: data.ORDER_ID,
        ipn_type: Number(data.IPN_TYPE_ID),
        ipn_type_name: data.IPN_TYPE_NAME,
        order_status: data.ORDER_STATUS,
        payment_method: data.PAYMENT_METHOD_NAME,
        amount: data.ORDER_TOTAL_AMOUNT,
        currency: data.ORDER_CURRENCY_CODE,
        customer_email: data.CUSTOMER_EMAIL,
        customer_name: data.CUSTOMER_NAME,
        update_time: new Date().toISOString(),
      })

    if (orderLogError) {
      console.error('Error logging webhook data:', orderLogError)
    }

    // 获取请求IP (需要根据您的部署环境调整获取方式)
    const clientIp =
      req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip')

    console.log('clientIp :>> ', clientIp)
    // IP 白名单验证
    const ipAddress = clientIp || '0.0.0.0'
    if (!ALLOWED_IPS.includes(ipAddress)) {
      console.error('Unauthorized IP:', ipAddress)
      return NextResponse.json(
        { success: false, message: 'Unauthorized IP' },
        { status: 403 }
      )
    }

    // 验证签名 (需要配置您的validation key)
    const signature = data.SIGNATURE
    // const validationKey = process.env.PAYPRO_VALIDATION_KEY;
    const validationKey = 'yQS5n66pQyUpQJBQ9Omq8MMyJvgB7_'
    if (!verifySignature(data, String(signature), validationKey)) {
      console.error('Invalid signature')
      return NextResponse.json(
        { success: false, message: 'Invalid signature' },
        { status: 401 }
      )
    }

    // 根据webhook类型处理不同的业务逻辑
    const ipnTypeId = Number(data.IPN_TYPE_ID)

    switch (ipnTypeId) {
      case WebhookType.OrderCharged:
        await handleOrderCharged(data)
        break
      case WebhookType.OrderRefunded:
        await handleOrderRefunded(data)
        break
      case WebhookType.OrderChargedBack:
        await handleOrderChargedBack(data)
        break
      case WebhookType.OrderDeclined:
        await handleOrderDeclined(data)
        break
      case WebhookType.OrderPartiallyRefunded:
        await handleOrderPartiallyRefunded(data)
        break
      case WebhookType.SubscriptionChargeSucceed:
        await handleSubscriptionChargeSucceed(data)
        break
      case WebhookType.SubscriptionChargeFailed:
        await handleSubscriptionChargeFailed(data)
        break
      case WebhookType.SubscriptionSuspended:
        await handleSubscriptionSuspended(data)
        break
      case WebhookType.SubscriptionRenewed:
        await handleSubscriptionChargeSucceed(data) // 使用相同的处理逻辑
        break
      case WebhookType.SubscriptionTerminated:
        await handleSubscriptionTerminated(data)
        break
      case WebhookType.SubscriptionFinished:
        await handleSubscriptionTerminated(data) // 使用相同的处理逻辑
        break
      default:
        console.log(`Unhandled webhook type: ${ipnTypeId}`)
    }

    const headers = new Headers()
    // headers.set("Location", "/en/auth/login");
    headers.set('Location', 'https://www.removeai.io/auth/login')

    return new Response(null, {
      status: 302,
      headers,
    })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return NextResponse.json(
      {
        success: false,
        message: 'Error processing but acknowledged',
      },
      { status: 200 }
    )
  }
}
