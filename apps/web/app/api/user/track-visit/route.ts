// apps/web/app/api/user/track-visit/route.ts
import { supabase } from '@/lib/supabaseClient'

export async function POST(req: Request) {
  try {
    const { userId } = await req.json()
    const headers = new Headers(req.headers)

    const ip =
      headers.get('x-real-ip') || headers.get('x-forwarded-for') || 'unknown'

    // 获取 Vercel 提供的地理位置信息
    const country = headers.get('x-vercel-ip-country') || 'unknown'
    const city = headers.get('x-vercel-ip-city') || 'unknown'
    const region = headers.get('x-vercel-ip-country-region') || 'unknown'
    const latitude = headers.get('x-vercel-ip-latitude')
    const longitude = headers.get('x-vercel-ip-longitude')

    // const { error } = await supabase.from('user_visits').insert({
    //   user_id: userId,
    //   ip_address: ip,
    //   country,
    //   city,
    //   region,
    //   latitude: latitude ? parseFloat(latitude) : null,
    //   longitude: longitude ? parseFloat(longitude) : null,
    // })

    // if (error) throw error

    return Response.json({
      success: true,
      data: {
        ip,
        location: {
          country,
          city,
          region,
          latitude: latitude ? parseFloat(latitude) : null,
          longitude: longitude ? parseFloat(longitude) : null,
        },
      },
    })
  } catch (error) {
    console.error('Error tracking visit:', error)
    return Response.json({ error: 'Failed to track visit' }, { status: 500 })
  }
}
