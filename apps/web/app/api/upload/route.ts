import { NextRequest, NextResponse } from 'next/server'
import OSS from 'ali-oss'
import { v4 as uuidv4 } from 'uuid'
//
//
const OAAconfig = {
  accessKeyId: process.env.ALIYUN_OSS_ACCESS_KEY_ID || '',
  accessKeySecret: process.env.ALIYUN_OSS_ACCESS_KEY_SECRET || '',
  region: process.env.ALIYUN_OSS_REGION || '',
  bucket: process.env.ALIYUN_OSS_BUCKET || '',
  endpoint: process.env.ALIYUN_OSS_ENDPOINT || '',
  secure: true,
}

//

// OSS 客户端配置
const ossClient = new OSS(OAAconfig)

export async function POST(request: NextRequest) {
  try {
    // 获取上传的文件
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { code: 400, msg: '没有找到上传的文件' },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      return NextResponse.json(
        { code: 400, msg: '只支持上传图片文件' },
        { status: 400 }
      )
    }

    // 验证文件大小（限制为 10MB）
    if (file.size > 10 * 1024 * 1024) {
      return NextResponse.json(
        { code: 400, msg: '文件大小不能超过 10MB' },
        { status: 400 }
      )
    }

    // 生成唯一的文件名
    const ext = file.name.split('.').pop()
    const fileName = `uploads/${uuidv4()}.${ext}`

    // 将文件转换为 Buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // 上传到 OSS
    const result = await ossClient.put(fileName, buffer, {
      headers: {
        'Content-Type': file.type,
      },
    })

    // 返回上传成功的文件 URL
    return NextResponse.json({
      code: 200,
      msg: '上传成功',
      data: {
        url: result.url,
      },
    })
  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      { code: 500, msg: '文件上传失败' },
      { status: 500 }
    )
  }
}
