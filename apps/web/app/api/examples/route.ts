import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabaseClient'

export async function GET() {
  try {
    // 从Supabase获取风格数据
    const { data, error } = await supabase
      .from('removeai_examples')
      .select('*')
      .order('id', { ascending: true })

    if (error) {
      console.error('获取风格数据错误:', error)
      return NextResponse.json({ error: '获取风格数据失败' }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('处理请求错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
