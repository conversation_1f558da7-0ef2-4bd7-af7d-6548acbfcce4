// apps/web/app/api/membership/points/consume/route.ts
// 消耗积分的接口
import { cookies } from 'next/headers'
import { supabase } from '../../../../lib/supabaseClient'

export async function POST(req: Request) {
  try {
    // 获取请求参数
    const { email, userId, consumePoints } = await req.json()

    // 验证参数
    if (!email || !userId || !consumePoints || consumePoints <= 0) {
      return new Response(
        JSON.stringify({
          error: 'Invalid parameters',
          message:
            'Email, userId and consumePoints are required. ConsumePoints must be greater than 0',
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }

    // 查询用户信息
    const { data: user, error: userError } = await supabase
      .from('removeai_user')
      .select('id, email, points')
      .eq('id', userId)
      .eq('email', email)
      .single()

    // 用户不存在或查询出错
    if (userError || !user) {
      return new Response(
        JSON.stringify({
          error: 'User not found',
          message: 'Unable to find user with provided email and id',
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }

    // 检查积分是否足够
    if (user.points < consumePoints) {
      return new Response(
        JSON.stringify({
          error: 'Insufficient points',
          message: `Current points (${user.points}) is less than required points (${consumePoints})`,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }

    // 计算新的积分
    const newPoints = user.points - consumePoints

    console.log(
      'faith=============newPoints,userId,email',
      newPoints,
      userId,
      email
    )
    // 更新用户积分
    const { data: updatedUser, error: updateError } = await supabase
      .from('removeai_user')
      .update({ points: newPoints })
      .eq('id', userId)
      .eq('email', email)
      .select('points')
      .single()

    console.log('faith=============updateErrorupdateError', updateError)

    if (updateError) {
      return new Response(
        JSON.stringify({
          error: 'Update failed',
          message: 'Failed to update user points',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      )
    }

    // 记录积分消耗历史（如果需要）
    const { error: historyError } = await supabase
      .from('removeai_points_history')
      .insert({
        user_id: userId,
        points: consumePoints,
        type: 'consume',
        description: 'Points consumption',
        before_points: user.points,
        after_points: newPoints,
        create_time: new Date().toISOString(), // 直接使用 ISO 格式的时间字符串
      })

    if (historyError) {
      console.error('Failed to record points history:', historyError)
      // 不影响主流程，只记录错误
    }

    // 返回更新后的积分
    return new Response(
      JSON.stringify({
        success: true,
        points: updatedUser.points,
        message: `Successfully consumed ${consumePoints} points`,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Points consumption error:', error)
    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  }
}
