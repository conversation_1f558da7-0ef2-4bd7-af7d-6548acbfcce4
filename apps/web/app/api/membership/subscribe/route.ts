// apps/web/app/api/membership/subscribe/route.ts
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import { supabase } from '../../../lib/supabaseClient'

// const supabase = createClient(
//   process.env.NEXT_PUBLIC_SUPABASE_URL!,
//   process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
// );
//

export async function POST(req: Request) {
  try {
    const {
      membershipLevelId,
      isYearly,
      paymentMethod = 'paypro',
      paymentChannel,
      isMobile = false,
    } = await req.json()
    // 0 免费
    // 1 基础 isYearly => 4
    // 2 高级 isYearly => 5
    // 3 无限 isYearly => 6

    const mappedMembershipLevelId = isYearly
      ? membershipLevelId === 1
        ? 4 // 基础年付 => 4
        : membershipLevelId === 2
        ? 5 // 高级年付 => 5
        : membershipLevelId === 3
        ? 6 // 无限年付 => 6
        : membershipLevelId // 免费或其他不变
      : membershipLevelId // 月付不变

    // 获取当前用户session
    const cookieStore = await cookies()
    console.log('cookieStore :>> ', cookieStore)
    const oauthEmail = cookieStore.get('oauth_email')

    console.log('oauthEmail :>> ', oauthEmail)
    if (!oauthEmail) {
      return new Response('Unauthorized', { status: 401 })
    }

    // 验证会员等级
    const { data: membershipLevel, error: membershipError } = await supabase
      .from('removeai_membership_level')
      .select('*')
      .eq('id', mappedMembershipLevelId)
      .single()

    if (membershipError || !membershipLevel) {
      return new Response('Invalid membership level', { status: 400 })
    }

    // 根据邮箱获取用户id
    const { data: user, error: userError } = await supabase
      .from('removeai_user')
      .select('id')
      .eq('email', oauthEmail.value)
      .single()

    if (userError || !user) {
      return new Response('User not found', { status: 404 })
    }
    console.log('user :>> ', user)

    // 创建订单 ORD+时间戳+随机数+平台标识
    const orderNumber = `ORD${Date.now()}${Math.floor(Math.random() * 1000)}-${
      isMobile ? 'M' : 'P'
    }`
    console.log('orderNumber :>> ', orderNumber)
    const { data: order, error: orderError } = await supabase
      .from('removeai_order')
      .insert({
        user_id: user.id,
        order_number: orderNumber, // 保持原有的订单号生成方式
        product_id:
          paymentMethod === 'paypro'
            ? membershipLevel.paypro_product_id
            : membershipLevel.id.toString(),
        amount: membershipLevel.price,
        currency: membershipLevel.currency,
        status: 'pending',
        payment_method: paymentMethod, // 记录支付方式
        paypro_order_id: null, // 新增字段，用于存储 PayPro 的订单ID
      })
      .select()
      .single()
    if (orderError) {
      console.log('orderError :>> ', orderError)
      return new Response('Failed to create order', { status: 500 })
    }

    // 构建支付URL
    const checkoutUrl = `https://store.payproglobal.com/checkout?products[1][id]=${membershipLevel.paypro_product_id}&internal_order_id=${order.id}&user_id=${user.id}&membership_level_id=${membershipLevel.id}`
    return new Response(
      JSON.stringify({
        orderId: order.id,
        orderNumber: orderNumber,
        checkoutUrl: checkoutUrl,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      }
    )
  } catch (error) {
    console.error('Subscription error:', error)
    return new Response(
      `Subscription error: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      { status: 400 }
    )
  }
}

export async function GET() {
  try {
    const { data: levels, error } = await supabase
      .from('removeai_membership_level')
      .select('*')
      .eq('is_active', true)
      .order('price', { ascending: true })

    if (error) {
      throw error
    }

    return new Response(JSON.stringify(levels), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    })
  } catch (error) {
    console.error('Error fetching membership levels:', error)
    return new Response(
      `Error fetching membership levels: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`,
      { status: 400 }
    )
  }
}
