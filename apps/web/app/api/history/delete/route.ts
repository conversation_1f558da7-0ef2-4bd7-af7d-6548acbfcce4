import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  try {
    const {
      ids, // 单个ID或ID数组
      userId,
      hardDelete = false, // 是否硬删除，默认为软删除
    } = await req.json()

    // 验证必填字段
    if (!ids || !userId) {
      return NextResponse.json(
        { error: 'IDs and user ID are required' },
        { status: 400 }
      )
    }

    // 确保ids是数组格式并转换为数字（兼容字符串和数字）
    const idsArray = Array.isArray(ids) ? ids : [ids]
    const numericIds = idsArray.map((id) => {
      const numericId = parseInt(id, 10)
      if (isNaN(numericId) || numericId <= 0) {
        throw new Error(
          `Invalid ID: ${id}. ID must be a valid positive number.`
        )
      }
      return numericId
    })

    if (hardDelete) {
      // 硬删除：直接从数据库删除记录
      const { error } = await supabase
        .from('removeai_history')
        .delete()
        .in('id', numericIds)
        .eq('user_id', userId)

      if (error) {
        console.error('Failed to delete history records:', error)
        return NextResponse.json(
          { error: 'Failed to delete history records' },
          { status: 500 }
        )
      }
    } else {
      // 软删除：设置deleted_at字段
      const { error } = await supabase
        .from('removeai_history')
        .update({
          deleted_at: new Date().toISOString(),
        })
        .in('id', numericIds)
        .eq('user_id', userId)
        .is('deleted_at', null) // 只删除未被删除的记录

      if (error) {
        console.error('Failed to delete history records:', error)
        return NextResponse.json(
          { error: 'Failed to delete history records' },
          { status: 500 }
        )
      }
    }

    return NextResponse.json({
      success: true,
      message: `Successfully deleted ${numericIds.length} history record(s)`,
      deletedCount: numericIds.length,
    })
  } catch (error) {
    console.error('Failed to delete history records:', error)
    return NextResponse.json(
      { error: 'Server error, please try again later' },
      { status: 500 }
    )
  }
}
