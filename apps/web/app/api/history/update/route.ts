import { supabase } from '@/lib/supabaseClient'
import { NextResponse } from 'next/server'

export async function POST(req: Request) {
  const body = await req.json()

  return updateHistoryRecord(body)
}

export async function updateHistoryRecord(body: any) {
  try {
    const {
      id,
      userId,
      status,
      inputParams,
      resultData,
      metadata,
      errorMessage,
      completedAt,
    } = body

    // 验证必填字段
    if (!id || !userId) {
      return NextResponse.json(
        { error: 'ID and user ID are required' },
        { status: 400 }
      )
    }

    // 将id转换为数字类型（兼容字符串和数字）
    const numericId = parseInt(id, 10)
    if (isNaN(numericId) || numericId <= 0) {
      return NextResponse.json(
        { error: 'ID must be a valid positive number' },
        { status: 400 }
      )
    }

    // 构建更新数据对象（只包含提供的字段）
    const updateData: any = {}

    if (status !== undefined) updateData.status = status
    if (inputParams !== undefined) updateData.input_params = inputParams
    if (resultData !== undefined) updateData.result_data = resultData
    if (metadata !== undefined) updateData.metadata = metadata
    if (errorMessage !== undefined) updateData.error_message = errorMessage
    if (completedAt !== undefined) updateData.completed_at = completedAt

    // 如果状态更新为SUCCESS或FAILED，且没有提供completedAt，自动设置完成时间
    if (
      status &&
      (status === 'SUCCESS' || status === 'FAILED') &&
      completedAt === undefined
    ) {
      updateData.completed_at = new Date().toISOString()
    }

    // 添加详细日志
    console.log(
      `Updating history record - ID: ${numericId}, User: ${userId}, Data:`,
      updateData
    )

    // 首先验证记录是否存在且属于该用户
    const { data: existingRecord, error: checkError } = await supabase
      .from('removeai_history')
      .select('id, user_id, external_task_id, status')
      .eq('id', numericId)
      .eq('user_id', userId)
      .is('deleted_at', null)
      .single()

    if (checkError || !existingRecord) {
      console.warn(
        `Record verification failed - ID: ${numericId}, User: ${userId}`,
        checkError
      )
      return NextResponse.json(
        { error: 'Record not found or no permission to access' },
        { status: 404 }
      )
    }

    console.log(`Verified existing record:`, existingRecord)

    // 检查任务是否已经完成，如果已完成则不需要更新
    if (
      existingRecord.status === 'SUCCESS' ||
      existingRecord.status === 'FAILED'
    ) {
      console.log(
        `Task ${numericId} is already completed with status: ${existingRecord.status}, skipping update`
      )
      return NextResponse.json({
        success: true,
        message: 'Task already completed, no update needed',
        data: existingRecord,
        skipped: true,
      })
    }

    // 更新历史记录
    const { data, error } = await supabase
      .from('removeai_history')
      .update(updateData)
      .eq('id', numericId)
      .eq('user_id', userId)
      .is('deleted_at', null) // 只更新未被删除的记录
      .select()

    if (error) {
      console.error(
        `Failed to update history record ID ${numericId} for user ${userId}:`,
        error
      )
      console.error('Update data was:', updateData)
      return NextResponse.json(
        { error: 'Failed to update history record', details: error.message },
        { status: 500 }
      )
    }

    if (!data || data.length === 0) {
      console.warn(
        `No record found or no permission - ID: ${numericId}, User: ${userId}`
      )
      return NextResponse.json(
        { error: 'Record not found or no permission to access' },
        { status: 404 }
      )
    }

    console.log(`Successfully updated history record ID ${numericId}:`, data[0])
    return NextResponse.json({
      success: true,
      message: 'History record updated successfully',
      data: data[0],
    })
  } catch (error) {
    console.error('Failed to update history record:', error)
    return NextResponse.json(
      { error: 'Server error, please try again later' },
      { status: 500 }
    )
  }
}
