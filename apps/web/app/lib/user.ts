import { getUserFromClientCookies } from '@/utils/client-cookies'

export async function fetchUserInfo() {
  try {
    const user = getUserFromClientCookies()

    if (!user || !user.email) {
      return null
    }
    try {
      const response = await fetch(
        `/api/user/info?email=${encodeURIComponent(user.email)}`,
        {
          cache: 'no-store',
          headers: {
            Accept: 'application/json',
          },
        }
      )

      if (!response.ok) {
        const data = await response.json()
        throw new Error(
          data.message || `HTTP error! status: ${response.status}`
        )
      }

      const data = await response.json()
      return data.data
    } catch (fetchError) {
      if (fetchError instanceof Error && fetchError.name === 'AbortError') {
        console.error('Request timeout while fetching user info')
        return null
      }
      throw fetchError
    }
  } catch (error) {
    console.error('Error in fetchUserInfo:', error)
    return null
  }
}

// 可以添加类型定义
export interface UserInfo {
  id: string
  email: string
  name: string
  // ... 其他用户信息字段
}

// 也可以添加其他相关的用户函数
export async function isUserLoggedIn() {
  const user = getUserFromClientCookies()
  return !!user?.email
}
