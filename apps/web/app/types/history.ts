/**
 * 历史记录相关的 TypeScript 类型定义
 */

import { TASK_TYPES } from '../../constants'

/**
 * 任务状态枚举
 * PENDING - 已创建但未开始处理
 * PROCESSING - 正在处理中
 * SUCCESS - 任务执行成功
 * FAILED - 任务执行失败
 */
export type TaskStatus = 'PENDING' | 'SUCCESS' | 'FAILED' | 'PROCESSING'

/**
 * 任务类型枚举
 */
export type TaskType = (typeof TASK_TYPES)[keyof typeof TASK_TYPES]

// 数据库表结构类型
export interface HistoryItem {
  id: number
  user_id: string | null
  task_type: TaskType
  external_task_id: string | null
  status: TaskStatus
  /** 任务输入参数 (JSON)
   * 存储调用第三方 API 时发送的完整参数体
   * 核心通用字段，方便排查问题和重新执行任务
   * 例如：{ "source_image": "...", "target_image": "..." } */
  input_params: Record<string, any> | null
  /** 任务结果数据 (JSON)
   * 存储 Webhook 返回的成功或失败信息
   * 核心通用字段，成功时存结果图 URL 列表，失败时存错误信息 */
  result_data: Record<string, any> | null
  /** 扩展元数据 (JSON)
   * 存储额外的元数据信息，如请求来源、用户设备信息等 */
  metadata: Record<string, any> | null
  /** 简化的错误信息 (TEXT)
   * 可选，用于在列表中快速展示失败原因
   * 可以从 result_data 中提取，冗余存储以方便查询 */
  error_message: string | null
  created_at: string
  updated_at: string
  completed_at: string | null
  /** 软删除时间 (DATETIME / TIMESTAMP)
   * 用于软删除功能，非空表示记录已被删除 */
  deleted_at: string | null
}

/**
 * 创建历史记录请求类型
 * 用于创建新的任务历史记录
 */
export interface CreateHistoryRequest {
  /** 用户ID，必填 */
  userId: string
  /** 任务类型，必填 */
  taskType: TaskType
  /** 外部API任务ID，可选 */
  externalTaskId?: string
  /** 任务状态，默认为PENDING */
  status?: TaskStatus
  /** 输入参数，JSON格式 */
  inputParams?: Record<string, any>
  /** 结果数据，JSON格式 */
  resultData?: Record<string, any>
  /** 元数据，JSON格式 */
  metadata?: Record<string, any>
  /** 错误信息，可选 */
  errorMessage?: string
  /** 完成时间，可选 */
  completedAt?: string
}
