'use client'

import { Button } from '@ui/components/button'
import { useModal } from '@shared/hooks/useModal'

/**
 * 全局弹窗使用示例
 * 展示如何在组件中使用各种弹窗功能
 */
export function ModalUsageExamples() {
  const {
    showLoginModal,
    showInsufficientCreditsModal,
    showConfirmModal,
    showDeleteConfirm,
    clearAllModals,
    isModalOpen,
  } = useModal()

  return (
    <div className="space-y-4 p-6">
      <h2 className="text-2xl font-bold mb-6">全局弹窗使用示例</h2>
      
      <div className="grid grid-cols-2 gap-4">
        {/* 登录弹窗 */}
        <Button
          onClick={() => showLoginModal({
            title: '需要登录',
            content: '请先登录以继续使用AI功能',
            props: { needBottomArea: true }
          })}
        >
          显示登录弹窗
        </Button>

        {/* 积分不足弹窗 */}
        <Button
          onClick={() => showInsufficientCreditsModal({
            content: '当前积分不足，无法生成图片'
          })}
        >
          显示积分不足弹窗
        </Button>

        {/* 普通确认弹窗 */}
        <Button
          onClick={() => showConfirmModal({
            title: '确认操作',
            content: '您确定要执行此操作吗？',
            onConfirm: () => {
              console.log('用户确认了操作')
            },
            onCancel: () => {
              console.log('用户取消了操作')
            }
          })}
        >
          显示确认弹窗
        </Button>

        {/* 删除确认弹窗 */}
        <Button
          variant="destructive"
          onClick={() => showDeleteConfirm(
            () => {
              console.log('删除操作已确认')
              // 执行删除逻辑
            },
            '重要文件'
          )}
        >
          显示删除确认弹窗
        </Button>

        {/* 警告确认弹窗 */}
        <Button
          variant="outline"
          onClick={() => showConfirmModal({
            title: '注意',
            content: '此操作可能会影响其他用户，是否继续？',
            variant: 'warning',
            onConfirm: () => {
              console.log('警告操作已确认')
            }
          })}
        >
          显示警告弹窗
        </Button>

        {/* 清空所有弹窗 */}
        <Button
          variant="secondary"
          onClick={() => clearAllModals()}
          disabled={!isModalOpen}
        >
          清空所有弹窗
        </Button>
      </div>

      {/* 状态显示 */}
      <div className="mt-6 p-4 bg-gray-100 rounded-lg">
        <p className="text-sm text-gray-600">
          当前弹窗状态: {isModalOpen ? '有弹窗显示' : '无弹窗'}
        </p>
      </div>

      {/* 实际业务使用示例 */}
      <div className="mt-8">
        <h3 className="text-lg font-semibold mb-4">实际业务场景示例</h3>
        <div className="space-y-2">
          <Button onClick={() => handleGenerateImage()}>
            生成AI图片（需要登录和积分）
          </Button>
          <Button onClick={() => handleDeleteImage()}>
            删除图片（需要确认）
          </Button>
        </div>
      </div>
    </div>
  )

  // 实际业务逻辑示例
  function handleGenerateImage() {
    // 检查用户是否登录
    const isLoggedIn = false // 从你的用户状态获取
    if (!isLoggedIn) {
      showLoginModal({
        title: 'AI图片生成',
        content: '登录后即可开始创作精美的AI图片'
      })
      return
    }

    // 检查用户积分
    const userCredits = 0 // 从你的用户状态获取
    if (userCredits <= 0) {
      showInsufficientCreditsModal({
        content: '生成AI图片需要消耗1个积分，请先充值'
      })
      return
    }

    // 执行生成逻辑
    console.log('开始生成AI图片...')
  }

  function handleDeleteImage() {
    showDeleteConfirm(
      () => {
        // 执行删除API调用
        console.log('图片删除成功')
      },
      'AI生成的图片'
    )
  }
}

/**
 * 在其他组件中的简单使用示例
 */
export function SimpleUsageExample() {
  const { showLoginModal, showConfirmModal } = useModal()

  const handleSomeAction = () => {
    // 简单的登录检查
    const isLoggedIn = false
    if (!isLoggedIn) {
      showLoginModal()
      return
    }

    // 执行需要登录的操作
    console.log('执行操作...')
  }

  const handleDeleteItem = () => {
    showConfirmModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除吗？',
      variant: 'destructive',
      onConfirm: () => {
        // 删除逻辑
        console.log('删除成功')
      }
    })
  }

  return (
    <div className="space-x-2">
      <Button onClick={handleSomeAction}>
        需要登录的操作
      </Button>
      <Button variant="destructive" onClick={handleDeleteItem}>
        删除项目
      </Button>
    </div>
  )
}