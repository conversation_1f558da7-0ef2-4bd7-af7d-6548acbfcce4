{"dependencies": {"@aws-sdk/client-s3": "3.437.0", "@docmee/sdk-ui": "^1.1.13", "@hookform/resolvers": "^3.9.1", "@jdion/tilt-react": "^1.0.0", "@microsoft/clarity": "^1.0.0", "@node-rs/argon2": "^2.0.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.47.8", "@tanstack/react-query": "^5.60.6", "@tanstack/react-table": "^8.20.5", "@trpc/client": "11.0.0-rc.638", "@trpc/react-query": "11.0.0-rc.638", "@types/jsonwebtoken": "^9.0.10", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.1.0", "ali-oss": "^6.22.0", "api": "workspace:*", "arctic": "^2.2.2", "boring-avatars": "^1.11.2", "change-case": "^5.4.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cropperjs": "1.6.2", "database": "workspace:*", "date-fns": "^4.1.0", "deepmerge": "^4.3.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-react": "^8.5.1", "framer-motion": "^11.13.5", "google-auth-library": "^10.2.0", "i18n": "workspace:*", "jotai": "2.8.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "logs": "workspace:*", "lucide-react": "^0.460.0", "mail": "workspace:*", "next": "15.0.3", "next-intl": "3.25.1", "next-themes": "^0.4.3", "nextjs-toploader": "^3.7.15", "nprogress": "^0.2.0", "oslo": "^1.2.1", "proxy-agent": "^6.5.0", "react": "19.0.0-rc-65a56d0e-20241020", "react-compare-image": "^3.5.0", "react-cropper": "^2.3.3", "react-dom": "19.0.0-rc-65a56d0e-20241020", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.2", "react-hot-toast": "^2.5.1", "react-image-crop": "^11.0.10", "react-intersection-observer": "^9.16.0", "react-masonry-css": "^1.0.16", "server-only": "^0.0.1", "sharp": "^0.33.5", "slugify": "^1.6.6", "superjson": "^2.2.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "utils": "workspace:*", "uuid": "^11.0.3", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@content-collections/core": "^0.7.3", "@content-collections/mdx": "^0.2.0", "@content-collections/next": "^0.2.4", "@mdx-js/mdx": "^3.1.0", "@shikijs/rehype": "^1.23.1", "@types/ali-oss": "^6.16.11", "@types/cookie": "^0.6.0", "@types/js-cookie": "^3.0.4", "@types/mdx": "^2.0.13", "@types/node": "22.9.0", "@types/nprogress": "^0.2.3", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "@types/uuid": "^10.0.0", "auth": "workspace:*", "autoprefixer": "10.4.20", "cypress": "^13.15.2", "dotenv-cli": "^7.4.2", "eslint": "^9", "eslint-config-next": "15.3.5", "markdown-toc": "^1.2.0", "mdx": "^0.3.1", "postcss": "8.4.49", "rehype-img-size": "^1.0.1", "start-server-and-test": "^2.0.8", "tailwind-config": "workspace:*", "tailwindcss": "3.4.15", "tsconfig": "workspace:*"}, "name": "web", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "e2e": "dotenv -c -e ../../.env -- start-server-and-test dev http://localhost:3000 \"cypress open --e2e\"", "lint": "next lint", "shadcn-ui": "pnpm dlx shadcn@latest", "start": "next start", "type-check": "tsc --noEmit"}, "version": "0.1.0"}