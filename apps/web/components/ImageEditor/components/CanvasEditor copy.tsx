'use client'

import React, { useRef, useEffect, useState, useCallback } from 'react'
import { ImageCanvas } from './ImageCanvas'
import { MaskCanvas, type MaskState, type BrushSettings } from './MaskCanvas'
import { EditorToolbar } from './EditorToolbar'
import { SparkleOverlay } from './SparkleOverlay'
import type { ImageData } from '..'

interface CanvasEditorProps {
  imageData: ImageData
  onProcessImage: (maskCanvas: HTMLCanvasElement) => void
  disabled?: boolean
  brushSettings?: BrushSettings
  onBrushSettingsChange?: (settings: BrushSettings) => void
  // Mask state management
  initialMaskState?: MaskState // Mask state with size info
  onMaskStateChange?: (maskState: MaskState) => void
  // History state management
  initialHistoryState?: { history: string[]; historyIndex: number }
  onHistoryStateChange?: (history: string[], historyIndex: number) => void
  // Processing state
  isProcessing?: boolean
  // Help callback
  onShowHelp?: () => void
  // Comparison feature
  processedImageUrl?: string | null
  // Background removal feature
  onRemoveBackground?: () => void
  isBackgroundProcessing?: boolean
  backgroundRemovedImageUrl?: string | null
  // Background replacement feature
  onReplaceBackground?: (backgroundUrl: string) => void
  // Clear all operations
  onClearAll?: () => void
  finalResult?: {
    url: string | null
    type: 'inpaint' | 'background' | 'blur' | 'final' | 'none'
  }
  // Background blur feature
  onBlurBackground?: (blurIntensity: number) => void
  isBackgroundBlurProcessing?: boolean
  backgroundBlurredImageUrl?: string | null
  disableZoom?: boolean
}

const CanvasEditorComponent = React.forwardRef<any, CanvasEditorProps>(
  (
    {
      imageData,
      onProcessImage,
      disabled = false,
      brushSettings: externalBrushSettings,
      onBrushSettingsChange,
      initialMaskState,
      onMaskStateChange,
      initialHistoryState,
      onHistoryStateChange,
      isProcessing = false,
      onShowHelp,
      processedImageUrl,
      onRemoveBackground,
      isBackgroundProcessing = false,
      backgroundRemovedImageUrl,
      onReplaceBackground,
      onBlurBackground,
      isBackgroundBlurProcessing = false,
      backgroundBlurredImageUrl,
      onClearAll,
      finalResult,
      disableZoom = true,
    },
    ref
  ) => {
    const maskCanvasRef = useRef<any>(null)
    const containerRef = useRef<HTMLDivElement>(null)

    const [showComparison, setShowComparison] = useState(false)
    const [comparisonProgress, setComparisonProgress] = useState(0)
    const [comparisonTargetProgress, setComparisonTargetProgress] = useState(0)
    const [blurIntensity, setBlurIntensity] = useState(20)
    const blurTimeoutRef = useRef<NodeJS.Timeout | null>(null)

    // Whether there is any painted mask to enable object removal
    const [canRemoveObjects, setCanRemoveObjects] = useState(false)
    // Track latest mask snapshot for robust detection
    const [lastMaskDataURL, setLastMaskDataURL] = useState<string | null>(null)

    // Canvas state
    const [zoom, setZoom] = useState(1)
    const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 })
    const [pan, setPan] = useState({ x: 0, y: 0 })
    const [isPanning, setIsPanning] = useState(false)
    const [lastPanPoint, setLastPanPoint] = useState({ x: 0, y: 0 })

    const defaultBrushSettings: BrushSettings = {
      size: 20,
      opacity: 100,
      color: '#ff3333',
      shape: 'circle',
    }

    const brushSettings = externalBrushSettings || defaultBrushSettings

    // Debounced blur function for real-time preview
    const handleBlurIntensityChange = useCallback(
      (newIntensity: number) => {
        setBlurIntensity(newIntensity)

        // Clear existing timeout
        if (blurTimeoutRef.current) {
          clearTimeout(blurTimeoutRef.current)
        }

        // Set new timeout for debounced blur application
        blurTimeoutRef.current = setTimeout(() => {
          if (onBlurBackground) {
            onBlurBackground(newIntensity)
          }
        }, 150) // 150ms debounce for faster response
      },
      [onBlurBackground]
    )

    const handleClearAll = useCallback(() => {
      if (maskCanvasRef.current?.clearAll) {
        maskCanvasRef.current.clearAll()
      }
      // After clearing, mask is empty
      setCanRemoveObjects(false)
      if (onClearAll) {
        onClearAll()
      }
    }, [onClearAll])

    useEffect(() => {
      console.log('useEffect')
      document.body.style.overflow = 'hidden'
      const footer = document.querySelector('footer')
      const originDisplay = footer?.style.display
      if (footer) {
        footer.style.display = 'none'
      }
      document.documentElement.style.overflow = 'hidden'
      return () => {
        document.body.style.overflow = ''
        document.documentElement.style.overflow = ''
        if (footer) {
          footer.style.display = originDisplay || ''
        }
      }
    }, [])

    // Helper: check alpha presence from a dataURL by scaling down to 64x64 to speed up
    const hasAlphaFromDataURL = useCallback(
      async (dataURL: string): Promise<boolean> => {
        return new Promise((resolve) => {
          const img = new Image()
          img.crossOrigin = 'anonymous'
          img.onload = () => {
            const w = 64
            const h = 64
            const off = document.createElement('canvas')
            off.width = w
            off.height = h
            const ictx = off.getContext('2d')
            if (!ictx) return resolve(false)
            ictx.clearRect(0, 0, w, h)
            // Draw scaled image
            ictx.drawImage(img, 0, 0, w, h)
            try {
              const data = ictx.getImageData(0, 0, w, h).data
              for (let i = 3; i < data.length; i += 4) {
                // check every pixel
                if (data[i] > 0) return resolve(true)
              }
              resolve(false)
            } catch {
              resolve(false)
            }
          }
          img.onerror = () => resolve(false)
          img.src = dataURL
        })
      },
      []
    )

    // Recompute whether mask has any painted pixels (non-zero alpha)
    const recomputeMaskNonEmpty = useCallback(async () => {
      // Prefer latest snapshot dataURL when available
      if (lastMaskDataURL) {
        const hasAlpha = await hasAlphaFromDataURL(lastMaskDataURL)
        setCanRemoveObjects(hasAlpha)
        return hasAlpha
      }
      const canvas: HTMLCanvasElement | null =
        maskCanvasRef.current?.getCanvas?.() || null
      if (!canvas) {
        setCanRemoveObjects(false)
        return false
      }
      const ctx = canvas.getContext('2d')
      if (!ctx) {
        setCanRemoveObjects(false)
        return false
      }
      const { width, height } = canvas
      if (!width || !height) {
        setCanRemoveObjects(false)
        return false
      }
      try {
        const data = ctx.getImageData(0, 0, width, height).data
        for (let i = 3; i < data.length; i += 64) {
          if (data[i] > 0) {
            setCanRemoveObjects(true)
            return true
          }
        }
        setCanRemoveObjects(false)
        return false
      } catch (e) {
        setCanRemoveObjects(false)
        return false
      }
    }, [lastMaskDataURL, hasAlphaFromDataURL])

    const handleProcess = useCallback(() => {
      if (maskCanvasRef.current?.getCanvas) {
        const canvas = maskCanvasRef.current.getCanvas()
        if (canvas) {
          onProcessImage(canvas)
        }
      }
    }, [onProcessImage])

    // Expose methods to parent component
    React.useImperativeHandle(ref, () => ({
      getMaskCanvas: () => {
        return maskCanvasRef.current?.getCanvas() || null
      },
    }))

    // Comparison handlers
    const handleCompareStart = () => {
      const comparisonImageUrl = finalResult?.url
      if (comparisonImageUrl) {
        setShowComparison(true)
        setComparisonTargetProgress(1) // Target is full progress
      }
    }

    const handleCompareEnd = () => {
      setComparisonTargetProgress(0) // Target is zero progress
    }

    // Animation effect for comparison
    useEffect(() => {
      if (!showComparison) return

      const animateProgress = () => {
        const currentProgress = comparisonProgress
        const targetProgress = comparisonTargetProgress

        if (Math.abs(currentProgress - targetProgress) < 0.01) {
          // Close enough to target, set exact value
          setComparisonProgress(targetProgress)
          if (targetProgress === 0) {
            setShowComparison(false)
          }
          return
        }

        // Calculate step size for smooth animation (same speed regardless of direction)
        const step = 0.025 // This gives us ~400ms for full sweep (1/0.025 = 40 frames at 60fps)
        const direction = targetProgress > currentProgress ? 1 : -1
        const newProgress = currentProgress + step * direction

        // Clamp to target if we would overshoot
        const clampedProgress =
          direction > 0
            ? Math.min(newProgress, targetProgress)
            : Math.max(newProgress, targetProgress)

        setComparisonProgress(clampedProgress)
        requestAnimationFrame(animateProgress)
      }

      const animationId = requestAnimationFrame(animateProgress)

      return () => {
        cancelAnimationFrame(animationId)
      }
    }, [showComparison, comparisonProgress, comparisonTargetProgress])

    // Cleanup on unmount
    useEffect(() => {
      return () => {
        setShowComparison(false)
        setComparisonProgress(0)
        setComparisonTargetProgress(0)
        // Clear blur timeout
        if (blurTimeoutRef.current) {
          clearTimeout(blurTimeoutRef.current)
        }
      }
    }, [])

    // Re-verify by alpha scan whenever we get a new snapshot
    useEffect(() => {
      recomputeMaskNonEmpty()
    }, [recomputeMaskNonEmpty, lastMaskDataURL])

    // Handle mouse wheel zoom
    const handleWheel = useCallback(
      (e: React.WheelEvent) => {
        e.preventDefault()
        const delta = e.deltaY > 0 ? -0.1 : 0.1
        const newZoom = Math.max(0.1, Math.min(3, zoom + delta))
        setZoom(newZoom)
      },
      [zoom]
    )

    // Handle pan start
    const handlePanStart = useCallback((e: React.MouseEvent) => {
      if (e.button === 1 || (e.button === 0 && e.altKey)) {
        // Middle mouse or Alt+Left mouse
        e.preventDefault()
        setIsPanning(true)
        setLastPanPoint({ x: e.clientX, y: e.clientY })
      }
    }, [])

    // Handle pan move
    const handlePanMove = useCallback(
      (e: React.MouseEvent) => {
        if (isPanning) {
          e.preventDefault()
          const deltaX = e.clientX - lastPanPoint.x
          const deltaY = e.clientY - lastPanPoint.y

          setPan((prev) => ({
            x: prev.x + deltaX,
            y: prev.y + deltaY,
          }))

          setLastPanPoint({ x: e.clientX, y: e.clientY })
        }
      },
      [isPanning, lastPanPoint]
    )

    // Handle pan end
    const handlePanEnd = useCallback(() => {
      setIsPanning(false)
    }, [])

    // Undo function
    const undo = useCallback(() => {
      if (maskCanvasRef.current?.undo) {
        maskCanvasRef.current.undo()
      }
    }, [])

    // Redo function
    const redo = useCallback(() => {
      if (maskCanvasRef.current?.redo) {
        maskCanvasRef.current.redo()
      }
    }, [])

    // Keyboard shortcuts
    useEffect(() => {
      const handleKeyDown = (e: KeyboardEvent) => {
        const isMac = /Mac|iPod|iPhone|iPad/.test(navigator.platform)
        const ctrlKey = isMac ? e.metaKey : e.ctrlKey

        if (ctrlKey && e.key === 'z' && !e.shiftKey) {
          e.preventDefault()
          undo()
        } else if (
          ctrlKey &&
          (e.key === 'y' || (e.key === 'z' && e.shiftKey))
        ) {
          e.preventDefault()
          redo()
        }
      }

      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }, [undo, redo])

    return (
      <div className="h-full flex flex-col">
        {/* Top toolbar */}
        <EditorToolbar
          canUndo={maskCanvasRef.current?.canUndo || false}
          canRedo={maskCanvasRef.current?.canRedo || false}
          onUndo={undo}
          onRedo={redo}
          onClearAll={handleClearAll}
          zoom={zoom}
          onZoomChange={setZoom}
          onPanChange={setPan}
          onCompareStart={handleCompareStart}
          onCompareEnd={handleCompareEnd}
          showCompare={
            !!(
              processedImageUrl ||
              backgroundRemovedImageUrl ||
              backgroundBlurredImageUrl
            )
          }
          onRemoveBackground={onRemoveBackground}
          isBackgroundProcessing={isBackgroundProcessing}
          backgroundRemovedImageUrl={backgroundRemovedImageUrl}
          onReplaceBackground={onReplaceBackground}
          originalImageUrl={imageData.url}
          onBlurBackground={onBlurBackground}
          isBackgroundBlurProcessing={isBackgroundBlurProcessing}
          backgroundBlurredImageUrl={backgroundBlurredImageUrl}
          blurIntensity={blurIntensity}
          onBlurIntensityChange={handleBlurIntensityChange}
          onRemoveObjects={handleProcess}
          isProcessing={isProcessing}
          disabled={disabled}
          canRemoveObjects={canRemoveObjects}
        />

        {/* Canvas Container */}
        <div className="flex-1 relative overflow-hidden">
          <div
            ref={containerRef}
            className="absolute inset-0 canvas-container flex items-center justify-center"
            onWheel={disableZoom ? undefined : handleWheel}
            onMouseDown={handlePanStart}
            onMouseMove={handlePanMove}
            onMouseUp={handlePanEnd}
            onMouseLeave={handlePanEnd}
            style={{ cursor: isPanning ? 'grabbing' : 'grab' }}
          >
            <div
              className="relative"
              style={{
                transform: `translate(${pan.x}px, ${pan.y}px) scale(${zoom})`,
                transformOrigin: 'center center',
                transition: isPanning ? 'none' : 'transform 0.1s ease-out',
              }}
            >
              <ImageCanvas
                imageData={imageData}
                finalResult={finalResult}
                canvasSize={canvasSize}
                showComparison={showComparison}
                comparisonProgress={comparisonProgress}
                onCanvasSizeChange={setCanvasSize}
              />

              <MaskCanvas
                key={imageData.id} // Force remount when switching images
                ref={maskCanvasRef}
                canvasSize={canvasSize}
                brushSettings={brushSettings}
                disabled={disabled}
                isPanning={isPanning}
                showComparison={showComparison}
                initialMaskState={initialMaskState}
                initialHistoryState={initialHistoryState}
                onMaskStateChange={(ms) => {
                  onMaskStateChange?.(ms)
                  setLastMaskDataURL(ms?.dataURL || null)
                }}
                onHistoryStateChange={(h, idx) => {
                  onHistoryStateChange?.(h, idx)
                  const latest = idx >= 0 ? h[idx] : null
                  if (latest) setLastMaskDataURL(latest)
                }}
                onStopDrawing={() => {
                  // will update via saveToHistory -> onHistoryStateChange
                }}
                zoom={zoom}
                isProcessing={
                  isProcessing ||
                  isBackgroundProcessing ||
                  isBackgroundBlurProcessing
                }
                processingType={
                  isBackgroundProcessing
                    ? 'background'
                    : isBackgroundBlurProcessing
                    ? 'blur'
                    : 'inpaint'
                }
              />

              {/* Sparkle overlay with background flash */}
              <SparkleOverlay
                isVisible={
                  isProcessing ||
                  isBackgroundProcessing ||
                  isBackgroundBlurProcessing
                }
                processingType={
                  isBackgroundProcessing
                    ? 'background'
                    : isBackgroundBlurProcessing
                    ? 'blur'
                    : 'inpaint'
                }
              />

              {/* Subtle processing indicator in corner */}
              {(isProcessing ||
                isBackgroundProcessing ||
                isBackgroundBlurProcessing) && (
                <div className="absolute top-4 right-4 flex items-center gap-2 bg-white/90 backdrop-blur-sm rounded-full px-3 py-2 shadow-lg z-40 pointer-events-none">
                  <div
                    className={`w-2 h-2 rounded-full animate-pulse ${
                      isBackgroundProcessing
                        ? 'bg-purple-500'
                        : isBackgroundBlurProcessing
                        ? 'bg-orange-500'
                        : 'bg-blue-500'
                    }`}
                  />
                  <span className="text-xs font-medium text-gray-700">
                    {isBackgroundProcessing
                      ? 'Removing BG'
                      : isBackgroundBlurProcessing
                      ? 'Blurring BG'
                      : 'Processing'}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }
)

// Custom comparison function to prevent re-renders when only brushSettings change
const arePropsEqual = (
  prevProps: CanvasEditorProps,
  nextProps: CanvasEditorProps
) => {
  // List of props to compare (excluding brushSettings)
  const propsToCompare: (keyof CanvasEditorProps)[] = [
    'imageData',
    'disabled',
    'initialMaskState',
    'initialHistoryState',
    'isProcessing',
    'processedImageUrl',
    'isBackgroundProcessing',
    'backgroundRemovedImageUrl',
    'isBackgroundBlurProcessing',
    'backgroundBlurredImageUrl',
    'finalResult',
  ]

  // Compare each prop individually
  for (const prop of propsToCompare) {
    if (prevProps[prop] !== nextProps[prop]) {
      return false
    }
  }

  // Ignore brushSettings changes - component will get latest via useRef
  return true
}

export const CanvasEditor = React.memo(CanvasEditorComponent, arePropsEqual)
