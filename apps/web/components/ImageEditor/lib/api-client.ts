// Secure API client for image processing operations
// All sensitive parameters are handled server-side

import { calculateObjectCover } from './image-utils'

export interface BackgroundRemovalOptions {
  model?:
    | 'u2net'
    | 'u2netp'
    | 'u2net_human_seg'
    | 'u2net_cloth_seg'
    | 'silueta'
    | 'isnet-general-use'
    | 'briaai/RMBG-1.4'
}

export interface BackgroundBlurOptions {
  blurIntensity?: number // blur intensity percentage (0-100), default 20
}

export interface InpaintingRequest {
  image: File
  mask: File
  prompt?: string
  negative_prompt?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  error?: string
  data?: T
}

// Background removal using secure API route
export const removeBackgroundSecure = async (
  imageBase64: string,
  options: BackgroundRemovalOptions = {}
): Promise<string> => {
  try {
    const response = await fetch('/api/background/remove', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        image: imageBase64,
        model: options.model || 'u2net',
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `API error: ${response.statusText}`)
    }

    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Background removal failed')
    }

    return result.imageBase64
  } catch (error) {
    console.error('Background removal error:', error)
    throw error
  }
}

// Background blur using secure API route
export const blurBackgroundSecure = async (
  originalImageBase64: string,
  removedBackgroundBase64: string,
  options: BackgroundBlurOptions = {}
): Promise<string> => {
  try {
    const response = await fetch('/api/background/blur', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        originalImageBase64,
        removedBackgroundBase64,
        blurIntensity: options.blurIntensity || 20,
      }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `API error: ${response.statusText}`)
    }

    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Background blur failed')
    }

    // Handle fallback to client-side processing if needed
    if (result.fallbackToClient) {
      // Import the client-side blur function
      const { blurBackgroundClient } = await import('./background-removal')
      return blurBackgroundClient(
        result.originalImageBase64,
        result.removedBackgroundBase64,
        result.blurIntensity
      )
    }

    return result.imageBase64
  } catch (error) {
    console.error('Background blur error:', error)
    throw error
  }
}

// Object removal using secure API route (enhanced existing)
export const removeObjectsSecure = async (
  request: InpaintingRequest,
  baseUrl?: string
): Promise<string> => {
  try {
    const formData = new FormData()
    formData.append('provider', 'iopaint')
    formData.append('image', request.image)
    formData.append('mask', request.mask)

    if (request.prompt) {
      formData.append('prompt', request.prompt)
    }

    if (request.negative_prompt) {
      formData.append('negative_prompt', request.negative_prompt)
    }

    if (baseUrl) {
      formData.append('baseUrl', baseUrl)
    }

    const response = await fetch('/api/inpaint', {
      method: 'POST',
      body: formData,
      cache: 'no-cache',
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || `API error: ${response.statusText}`)
    }

    const result = await response.json()
    if (!result.success) {
      throw new Error(result.error || 'Object removal failed')
    }

    return result.imageUrl
  } catch (error) {
    console.error('Object removal error:', error)
    throw error
  }
}

// Utility function to convert canvas to base64
export const canvasToBase64 = (canvas: HTMLCanvasElement): string => {
  return canvas.toDataURL('image/png').split(',')[1]
}

// Utility function to convert base64 to File
export const base64ToFile = (
  base64: string,
  filename: string,
  mimeType: string = 'image/png'
): File => {
  // Remove data URL prefix if present
  const cleanBase64 = base64.replace(/^data:image\/[a-z]+;base64,/, '')

  const byteCharacters = atob(cleanBase64)
  const byteNumbers = new Array(byteCharacters.length)
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  const byteArray = new Uint8Array(byteNumbers)
  const blob = new Blob([byteArray], { type: mimeType })
  return new File([blob], filename, { type: mimeType })
}

// Utility function to load image from URL
export const loadImage = (url: string): Promise<HTMLImageElement> => {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = url
  })
}

// Client-side image composition (for background replacement)
export const compositeWithBackgroundSecure = async (
  foregroundImageUrl: string,
  backgroundImageUrl: string,
  targetWidth: number,
  targetHeight: number
): Promise<string> => {
  try {
    // Load both images
    const [bgImg, fgImg] = await Promise.all([
      loadImage(backgroundImageUrl),
      loadImage(foregroundImageUrl),
    ])

    // Create canvas
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Failed to get canvas context')
    }

    canvas.width = targetWidth
    canvas.height = targetHeight

    // Calculate object-cover dimensions for background image
    const imageDimensions = { width: bgImg.width, height: bgImg.height }
    const canvasDimensions = { width: targetWidth, height: targetHeight }
    const { drawWidth, drawHeight, drawX, drawY } = calculateObjectCover(
      imageDimensions,
      canvasDimensions
    )

    // Draw background with object-cover behavior (maintains aspect ratio and fills container)
    ctx.drawImage(bgImg, drawX, drawY, drawWidth, drawHeight)

    // Draw foreground (preserve original size and position)
    ctx.drawImage(fgImg, 0, 0)

    const resultDataURL = canvas.toDataURL('image/png')
    return resultDataURL
  } catch (error) {
    throw new Error(
      `Failed to composite images: ${
        error instanceof Error ? error.message : 'Unknown error'
      }`
    )
  }
}
