项目根目录 addTranslation.js 参考这个文件，主要是 translations ，我需要你帮我抽离 apps/web/app/[locale]/(seo)/tools/ai-colorize 这个页面以及组件内所有的静态文案翻译成我们脚本 translations 需要的格式，一次性翻译完 13 个语言，然后让他用 t 方法调用多语言替换原本的静态文案；

import { getLocale, getTranslations } from 'next-intl/server'
const t = await getTranslations()
{ t('blog.description') }


请帮我将 apps/web/app/[locale]/(seo)/tools/colorize 页面和组件中的所有静态文案抽离并翻译成 13 种语言，具体语言为：德语 (de)、英语 (en)、西班牙语 (es)、法语 (fr)、日语 (ja)、韩语 (ko)、葡萄牙语 (pt)、俄语 (ru)、泰语 (th)、越南语 (vi)、简体中文 (zh-CN)、香港繁体中文 (zh-HK)、台湾繁体中文 (zh-TW)

要求：

1. 根目录下有 addTranslation.js 脚本，其中包含 translations 变量，translations 变量包含 13 个国家的语言, 存储所有语言的翻译
2. translations 变量结构应为平级，使用驼峰命名，以 colorize 为前缀，如：
   const translations = {
   en: {
   colorize: {
   'aicolorizePageTitle': 'xxxxx',
   'aicolorizeDescription': 'xxxxx',
   }
   },
   de : {
   colorize: {
   ....
   }
   }
   }

3、英文版本(en)必须与原文保持一致
4. 翻译完成后，你不用到 packages/i18n/translations 下帮我一个一个翻译， 而是帮我执行 node addTranslation.js, 会自动将翻译内容填充到里面
5.参考代码

import { Mail } from 'lucide-react'
import { getTranslations } from 'next-intl/server'

export async function generateMetadata() {
const t = await getTranslations()
return {
title: t('seo-contact-t'),
description: t('seo-contact-d'),
keywords: t('seo-contact-k'),
}
}

export default async function PricingPage() {
const t = await getTranslations()

return (
<main className="px-8 pt-40 pb-24 relative bg-gradient-to-r from-[#1a0033] via-[#0a001a] to-[#1a0033] min-h-screen text-white">
{/_ 装饰性背景元素 _/}
<div className="absolute inset-0 pointer-events-none overflow-hidden">
<div className="absolute -top-40 -right-40 w-96 h-96 bg-purple-500 opacity-10 rounded-full blur-3xl"></div>
<div className="absolute -bottom-20 -left-20 w-72 h-72 bg-fuchsia-500 opacity-10 rounded-full blur-3xl"></div>

        {/* 网格装饰 */}
        <div className="absolute inset-0 opacity-[0.05]">
          <div
            className="h-full w-full"
            style={{
              backgroundImage:
                'linear-gradient(rgba(168, 85, 247, 0.3) 1px, transparent 1px), linear-gradient(to right, rgba(168, 85, 247, 0.3) 1px, transparent 1px)',
              backgroundSize: '32px 32px',
            }}
          ></div>
        </div>
      </div>

      <section className="py-12 md:py-16 relative z-10 mt-[240px]">
        <div className="max-w-7xl mx-auto px-4">
          <div className="max-w-2xl">
            <div className="mb-6">
              <div className="inline-block p-3 rounded-xl bg-purple-500/20 backdrop-blur-sm border border-purple-500/30 mb-4">
                <Mail className="w-6 h-6 text-purple-400" />
              </div>

              <h2 className="text-4xl font-bold mb-4 bg-gradient-to-r from-purple-400 via-fuchsia-400 to-indigo-400 bg-clip-text text-transparent">
                {t('contact.title')}
              </h2>

              <p className="text-lg text-gray-300 leading-relaxed mb-8">
                {t('contact.description')}
              </p>
            </div>

            <div className="space-x-4 flex items-center">
              <h3 className="text-gray-300">{t('contact.email.label')}</h3>
              <a
                href="mailto:<EMAIL>"
                className="text-lg text-white hover:text-purple-400 transition-colors font-medium"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </section>
    </main>

)
}
