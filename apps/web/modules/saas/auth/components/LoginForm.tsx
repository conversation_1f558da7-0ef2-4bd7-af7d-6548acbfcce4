'use client'

import { useTranslations } from 'next-intl'
import { z } from 'zod'
import { SocialSigninButton, oAuthProviders } from './SocialSigninButton'

const formSchema = z.object({
  email: z.string().email(),
  password: z.optional(z.string()),
})

type FormValues = z.infer<typeof formSchema>

export function LoginForm({
  needBackground = true,
}: {
  needBackground?: boolean
}) {
  const t = useTranslations()

  return (
    <div
      className={`rounded-3xl w-full p-8 max-w-md mx-auto ${
        needBackground
          ? 'bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm shadow-xl shadow-purple-500/20 border border-purple-500/20'
          : ''
      }`}
    >
      {/* 标题部分 */}
      <div className="space-y-3 mb-8">
        <h1 className="font-extrabold text-4xl md:text-5xl bg-gradient-to-r from-indigo-400 via-purple-400 to-pink-400 bg-clip-text text-transparent drop-shadow-[0_2px_16px_rgba(99,102,241,0.5)]">
          {t('auth.login.title')}
        </h1>
        <p className="text-lg text-gray-300">{t('auth.login.subtitle')}</p>
      </div>

      {/* 特性列表 */}
      <div className="space-y-6 mb-8">
        <FeatureItem
          icon="magic"
          title="More Free Credits"
          description="Get additional credits for AI image generation"
        />
        <FeatureItem
          icon="download"
          title="Download Images"
          description="Save and download your generated images in high quality"
        />
        <FeatureItem
          icon="storage"
          title="Permanent Storage"
          description="Safely store your image generation history and creations"
        />
      </div>

      {/* 社交登录按钮 */}
      <div className="w-full space-y-4">
        {Object.keys(oAuthProviders).map((providerId) => (
          <SocialSigninButton key={providerId} provider={providerId} />
        ))}
      </div>

      <hr className="my-8 border-gray-600" />
    </div>
  )
}

// 特性项组件
function FeatureItem({
  icon,
  title,
  description,
}: {
  icon: keyof typeof iconMap
  title: string
  description: string
}) {
  const iconMap = {
    magic: (
      <svg
        className="w-6 h-6 text-indigo-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
        />
      </svg>
    ),
    download: (
      <svg
        className="w-6 h-6 text-indigo-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
        />
      </svg>
    ),
    storage: (
      <svg
        className="w-6 h-6 text-indigo-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"
        />
      </svg>
    ),
  }

  return (
    <div className="flex items-start space-x-4">
      <div className="bg-indigo-400/10 p-3 rounded-xl">{iconMap[icon]}</div>
      <div>
        <h3 className="font-semibold text-gray-200">{title}</h3>
        <p className="text-gray-400">{description}</p>
      </div>
    </div>
  )
}
