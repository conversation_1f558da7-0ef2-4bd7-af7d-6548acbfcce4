'use client'

import { Button } from '@ui/components/button'
import { useTranslations } from 'next-intl'
import type { JSXElementConstructor } from 'react'
import type React from 'react'

type IconProps = {
  className?: string
}

export const oAuthProviders: Record<
  string,
  {
    name: string
    icon: JSXElementConstructor<React.SVGProps<SVGSVGElement>>
  }
> = {
  google: {
    name: 'Google',
    icon: ({ ...props }: IconProps) => (
      <svg viewBox="0 0 488 512" {...props}>
        <title>Google</title>
        <path
          d="M488 261.8C488 403.3 391.1 504 248 504 110.8 504 0 393.2 0 256S110.8 8 248 8c66.8 0 123 24.5 166.3 64.9l-67.5 64.9C258.5 52.6 94.3 116.6 94.3 256c0 86.5 69.1 156.6 153.7 156.6 98.2 0 135-70.4 140.8-106.9H248v-85.3h236.1c2.3 12.7 3.9 24.9 3.9 41.4z"
          fill="currentColor"
        />
      </svg>
    ),
  },
}

export function SocialSigninButton({
  provider,
  className,
}: {
  provider: keyof typeof oAuthProviders
  className?: string
}) {
  const t = useTranslations()
  const providerData = oAuthProviders[provider]

  return (
    <Button
      asChild
      type="button"
      className={`group w-full h-14 bg-gradient-to-r from-[#4B6BFB] to-[#2F4BFB] hover:from-[#4B6BFB]/90 hover:to-[#2F4BFB]/90 text-white border-none transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg shadow-[#4B6BFB]/20 rounded-xl ${className}`}
    >
      <a
        href={`/api/oauth/${provider}`}
        className="relative flex items-center justify-center w-full"
      >
        <div className="absolute left-4 bg-white p-2 rounded-lg">
          {providerData.icon && (
            <providerData.icon className="w-5 h-5 text-[#4B6BFB]" />
          )}
        </div>
        <span className="text-base font-medium">
          {t('auth.continueWithProvider', { provider: providerData.name })}
        </span>
      </a>
    </Button>
  )
}
