import { cn } from '@ui/lib'

interface AvatarProps {
  src?: string | null
  alt: string
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

const sizeClasses = {
  sm: 'h-6 w-6 min-w-[24px] text-xs',
  md: 'h-7 w-7 min-w-[28px] text-sm',
  lg: 'h-8 w-8 min-w-[32px] text-base',
}

export function Avatar({ src, alt, className, size = 'md' }: AvatarProps) {
  const initials = alt.substring(0, 2).toUpperCase()
  const sizeClass = sizeClasses[size]

  if (src) {
    return (
      <img
        src={src}
        alt={alt}
        className={cn(
          sizeClass,
          'rounded-full object-cover flex-shrink-0 ring-2 ring-white',
          className
        )}
      />
    )
  }

  return (
    <div
      className={cn(
        sizeClass,
        'flex items-center justify-center rounded-full bg-[#4B6BFB]/10 font-medium flex-shrink-0 text-[#4B6BFB]',
        className
      )}
    >
      {initials}
    </div>
  )
}
