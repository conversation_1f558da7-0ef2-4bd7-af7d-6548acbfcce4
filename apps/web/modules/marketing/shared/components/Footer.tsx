'use client'
import { Link, usePathname } from '@i18n/routing'
import { Logo, getMenuCategories } from '@shared/components/Logo'
import { useTranslations } from 'next-intl'

const HotLabel = () => (
  <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-indigo-600 px-2 py-1 text-sm font-medium text-white ml-1 shadow-sm">
    Hot
  </span>
)

const NewLabel = () => (
  <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-emerald-600 px-2 py-1 text-sm font-medium text-white ml-1 shadow-sm">
    New
  </span>
)

const ComingLabel = () => (
  <span className="inline-flex items-center justify-center  px-2 py-1 text-sm font-medium rounded ml-1 whitespace-nowrap border border-orange-500/30 text-orange-600">
    Coming
  </span>
)

export function Footer() {
  const t = useTranslations()
  const pathname = usePathname()
  const isAiPage = pathname.startsWith('/ai/')
  const menuCategories = getMenuCategories(isAiPage, t)

  return (
    <footer className="py-12 bg-gray-50/80 border-t border-gray-200">
      <div className="container">
        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-8 gap-6 mb-8">
          {/* Logo和公司信息 */}
          <div className="lg:col-span-2">
            <Logo />
            <p className="mt-3 text-lg text-gray-600">
              {t('imageTools.platformDescription')}
            </p>
            <p className="mt-2 text-lg text-gray-500">
              © {new Date().getFullYear()} <EMAIL>
            </p>
          </div>

          {/* 工具分类展示 */}
          <div className="lg:col-span-6 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {Object.entries(menuCategories).map(([key, category]) => (
              <div key={key} className="space-y-3">
                <h3 className="font-semibold text-gray-800 text-lg uppercase tracking-wide">
                  {category.label}
                </h3>
                <ul className="space-y-2">
                  {category.items
                    .filter((item: any) => !item.hidden && !item.comingSoon)
                    .slice(0, 6)
                    .map((item: any, index: number) => (
                      <li key={index}>
                        {item.coming ? (
                          <></>
                        ) : (
                          // 先不展示 coming
                          // <div className="text-sm text-gray-500 cursor-not-allowed flex items-center opacity-60">
                          //   <span className="mr-2">{item.title}</span>
                          //   {item.isHot && <HotLabel />}
                          //   {item.isNew && <NewLabel />}
                          //   {item.coming && <ComingLabel />}
                          // </div>
                          <Link
                            href={item.href}
                            className="text-lg text-gray-600 hover:text-blue-600 transition-colors duration-200 flex items-center group"
                          >
                            <span className="group-hover:translate-x-1 transition-transform duration-200 mr-2">
                              {item.title}
                            </span>
                            {item.isHot && <HotLabel />}
                            {item.isNew && <NewLabel />}
                          </Link>
                        )}
                      </li>
                    ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* 底部法律信息 */}
        <div className="pt-6 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex flex-wrap gap-6">
              <Link
                href="/legal/privacy-policy"
                className="text-lg text-gray-600 hover:text-blue-600 transition-colors"
              >
                {t('privacyPolicy')}
              </Link>
              <Link
                href="/legal/terms"
                className="text-lg text-gray-600 hover:text-blue-600 transition-colors"
              >
                {t('termsAndConditions')}
              </Link>
              <Link
                href="/contact"
                className="text-lg text-gray-600 hover:text-blue-600 transition-colors"
              >
                {t('contact.text')}
              </Link>
              <Link
                href="/about"
                className="text-lg text-gray-600 hover:text-blue-600 transition-colors"
              >
                {t('aboutUs')}
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
