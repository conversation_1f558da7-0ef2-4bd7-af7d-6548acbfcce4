'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useTranslations } from 'next-intl'

interface Heading {
  slug: string
  content: string
  lvl: number
}

interface TableOfContentsProps {
  headings: Heading[]
}

function decodeEscaped(str: string) {
  return str.replace(/\\x([0-9A-Fa-f]{2})/g, (_, hex) =>
    String.fromCharCode(parseInt(hex, 16))
  )
}

export function TableOfContents({ headings }: TableOfContentsProps) {
  const t = useTranslations()
  const [activeId, setActiveId] = useState<string>('')
  const [tocStyle, setTocStyle] = useState({})
  const tocRef = useRef<HTMLDivElement>(null)
  const activeItemRef = useRef<HTMLAnchorElement>(null)
  const isScrollingRef = useRef(false)
  const lastScrollPositionRef = useRef(0)

  useEffect(() => {
    if (headings.length === 0) return

    const observer = new IntersectionObserver(
      (entries) => {
        if (isScrollingRef.current) return

        const visibleEntries = entries.filter((entry) => entry.isIntersecting)
        if (visibleEntries.length > 0) {
          const sortedEntries = visibleEntries.sort(
            (a, b) =>
              Math.abs(a.boundingClientRect.top - 100) -
              Math.abs(b.boundingClientRect.top - 100)
          )
          setActiveId(sortedEntries[0].target.id)
        } else {
          // 没有可见标题时，判断滚动方向
          const currentScrollPosition = window.scrollY
          const scrollingUp =
            currentScrollPosition < lastScrollPositionRef.current
          lastScrollPositionRef.current = currentScrollPosition

          // 如果向上滚动且已经接近顶部，激活第一个标题
          if (
            scrollingUp &&
            currentScrollPosition < 200 &&
            headings.length > 0
          ) {
            setActiveId(headings[0].slug)
          }
        }
      },
      {
        rootMargin: '-100px 0px -66%',
        threshold: 0,
      }
    )

    headings.forEach((heading) => {
      const element = document.getElementById(heading.slug)
      if (element) observer.observe(element)
    })

    // 添加滚动事件监听器来跟踪滚动方向
    const handleScroll = () => {
      const currentScrollPosition = window.scrollY

      // 接近顶部时激活第一个标题
      if (currentScrollPosition < 100 && headings.length > 0) {
        setActiveId(headings[0].slug)
      }
      // 接近底部时激活最后一个标题
      else if (
        window.innerHeight + currentScrollPosition >=
          document.body.offsetHeight - 100 &&
        headings.length > 0
      ) {
        setActiveId(headings[headings.length - 1].slug)
      }

      lastScrollPositionRef.current = currentScrollPosition
    }

    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      headings.forEach((heading) => {
        const element = document.getElementById(heading.slug)
        if (element) observer.unobserve(element)
      })
      window.removeEventListener('scroll', handleScroll)
    }
  }, [headings])

  // 当活动项目改变时，将其滚动到可见区域
  useEffect(() => {
    if (tocRef.current) {
      const activeIndex = headings.findIndex(
        (heading) => heading.slug === activeId
      )

      if (activeIndex === 0) {
        // 如果是第一个项目，滚动到顶部
        tocRef.current.scrollTop = 0
      } else if (activeIndex === headings.length - 1) {
        // 如果是最后一个项目，滚动到底部
        tocRef.current.scrollTop = tocRef.current.scrollHeight
      } else if (activeItemRef.current) {
        // 其他情况，使用正常的scrollIntoView
        activeItemRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
        })
      }
    }
  }, [activeId, headings])

  // 设置目录的位置
  useEffect(() => {
    if (!tocRef.current) return

    const updateTocPosition = () => {
      const containerElement = document.querySelector('.container')
      if (!containerElement) return

      const containerRect = containerElement.getBoundingClientRect()
      const leftPosition = containerRect.left + 24

      setTocStyle({
        position: 'fixed',
        left: `${leftPosition}px`,
        width: `240px`,
        transition: 'top 0.2s ease-out',
        top: '120px',
      })
    }

    updateTocPosition()
    window.addEventListener('resize', updateTocPosition)

    return () => {
      window.removeEventListener('resize', updateTocPosition)
    }
  }, [])

  if (headings.length === 0) {
    return null
  }

  return (
    <div
      ref={tocRef}
      style={tocStyle}
      className="max-h-[calc(100vh-150px)] overflow-y-auto p-4 rounded-lg border border-zinc-800/50 bg-zinc-900/50 backdrop-blur-sm shadow-lg z-10 toc-scrollbar"
    >
      <h2 className="text-lg font-bold mb-3 text-zinc-200">
        Table of Contents
      </h2>
      <nav>
        <ul className="space-y-2">
          {headings.map((heading, idx) => (
            <li
              key={`${heading.slug}-${heading.lvl}-${idx}`}
              className={`text-sm ${
                heading.lvl === 3 ? 'ml-4' : heading.lvl === 4 ? 'ml-8' : ''
              }`}
            >
              <a
                ref={activeId === heading.slug ? activeItemRef : null}
                href={`#${heading.slug}`}
                className={`block py-1 hover:text-primary transition-colors duration-200 ${
                  activeId === heading.slug
                    ? 'text-primary font-medium'
                    : 'text-zinc-400'
                }`}
                onClick={(e) => {
                  e.preventDefault()
                  const element = document.getElementById(heading.slug)
                  if (element) {
                    isScrollingRef.current = true

                    const yOffset = -100
                    const y =
                      element.getBoundingClientRect().top +
                      window.pageYOffset +
                      yOffset

                    setActiveId(heading.slug)

                    window.scrollTo({ top: y, behavior: 'smooth' })

                    window.history.pushState({}, '', `#${heading.slug}`)

                    setTimeout(() => {
                      isScrollingRef.current = false
                    }, 500)
                  }
                }}
              >
                {decodeEscaped(heading.content)}
              </a>
            </li>
          ))}
        </ul>
      </nav>
      <style jsx global>{`
        /* 自定义滚动条样式 */
        .toc-scrollbar::-webkit-scrollbar {
          width: 4px;
        }

        .toc-scrollbar::-webkit-scrollbar-track {
          background: rgba(30, 30, 30, 0.2);
          border-radius: 4px;
        }

        .toc-scrollbar::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.2);
          border-radius: 4px;
        }

        .toc-scrollbar::-webkit-scrollbar-thumb:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      `}</style>
    </div>
  )
}
