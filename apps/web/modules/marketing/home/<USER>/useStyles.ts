import { useQuery } from '@tanstack/react-query'
import type { StyleType } from '../state'

// 获取所有风格数据的单一接口
const fetchAllStyles = async (): Promise<StyleType[]> => {
  const response = await fetch('/api/examples')

  if (!response.ok) {
    throw new Error(`获取风格数据失败: ${response.status}`)
  }

  const data = await response.json()

  // 将从API获取的数据转换为StyleType格式
  return data.map((item: any) => ({
    id: item.id,
    title: item.title,
    category: item.category,
    prompt: item.prompt,
    originalImage: item.original_image,
    generatedImage: item.generated_image,
    showPrompt: item.show_prompt,
    showButton: item.show_button,
    ratio: item.ratio,
    /** 是否可用于风格选择 */
    showInStyles: item.show_in_styles,
    /** type: text-to-image|image-to-image */
    type: item.type,
  }))
}

// Hook：获取所有风格数据的基础查询
const useBaseStyles = () => {
  return useQuery({
    queryKey: ['images-examples-all'],
    queryFn: fetchAllStyles,
    staleTime: 5 * 60 * 1000, // 5分钟内认为数据是新鲜的，不会重新获取
    refetchOnWindowFocus: false, // 窗口获得焦点时不重新获取数据
    retry: 3, // 失败时重试3次
    retryDelay: 1000, // 重试间隔1秒
    refetchOnMount: true, // 组件挂载时重新获取数据
    refetchOnReconnect: true, // 网络重连时重新获取数据
  })
}

// Hook：获取所有风格数据（用于展示，不过滤）
export function useAllStyles() {
  return useBaseStyles()
}

// Hook：获取可用于风格选择的数据（过滤后）
export function useStyles(selectedStyleId?: number, mode?: string) {
  const baseQuery = useBaseStyles()

  // 去重处理（保留第一个出现的同名风格）
  let deduplicatedData =
    baseQuery.data
      ?.filter((item: StyleType) => {
        // 先过滤出可用于选择的风格
        if (!item.showInStyles) return false
        // 如果指定了 mode，则还要根据 type 过滤
        if (mode && item.type !== mode) return false
        return true
      })
      ?.reduce((unique: StyleType[], current: StyleType) => {
        // 检查是否已经存在相同标题的风格
        const existingStyle = unique.find(
          (style) => style.title === current.title
        )
        if (!existingStyle) {
          // 如果不存在相同标题的风格，则添加当前风格
          unique.push(current)
        }
        // 如果已存在相同标题的风格，则跳过当前风格（保留第一个出现的）
        return unique
      }, []) || []

  // 如果有选中的风格ID，确保它包含在结果中（即使被去重过滤掉了）
  if (selectedStyleId && baseQuery.data) {
    const selectedStyle = baseQuery.data.find(
      (style) =>
        style.id === selectedStyleId &&
        style.showInStyles &&
        (!mode || style.type === mode) // 确保选中的风格也符合当前模式
    )
    if (
      selectedStyle &&
      !deduplicatedData.find((style) => style.id === selectedStyleId)
    ) {
      // 如果选中的风格不在去重后的数据中，将其添加进去
      deduplicatedData.push(selectedStyle)
    }
  }

  return {
    ...baseQuery,
    data: deduplicatedData,
  }
}
