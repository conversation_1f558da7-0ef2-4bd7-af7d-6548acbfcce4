import { atom } from 'jotai'
import { atomWithStorage } from 'jotai/utils'

export type Theme = 'light' | 'dark'

// 使用 atomWithStorage 来持久化主题设置
export const themeAtom = atomWithStorage<Theme>('theme', 'dark')

// 计算属性：判断是否为亮色主题
export const isLightThemeAtom = atom((get) => get(themeAtom) === 'light')

// 计算属性：判断是否为暗色主题
export const isDarkThemeAtom = atom((get) => get(themeAtom) === 'dark')

// 切换主题的 action atom
export const toggleThemeAtom = atom(null, (get, set) => {
  const currentTheme = get(themeAtom)
  const newTheme: Theme = currentTheme === 'light' ? 'dark' : 'light'
  set(themeAtom, newTheme)
  // DOM 同步由 ThemeProvider 统一处理，避免竞态条件
  return newTheme
})

// 设置主题的 action atom
export const setThemeAtom = atom(null, (get, set, newTheme: Theme) => {
  set(themeAtom, newTheme)
  // DOM 同步由 ThemeProvider 统一处理，避免竞态条件
  return newTheme
})
