import { atom } from 'jotai'
import { UserInfo } from './types'

export const userInfoAtom = atom<UserInfo | null>(null)

export const consumePointsAtom = atom(
  (get) => get(userInfoAtom)?.points,
  (get, set, amount: number) => {
    const currentUserInfo = get(userInfoAtom)
    if (currentUserInfo) {
      // Optimistic update
      const newPoints = Math.max(0, currentUserInfo.points - amount)
      set(userInfoAtom, {
        ...currentUserInfo,
        points: newPoints,
      })
    }
  }
)
