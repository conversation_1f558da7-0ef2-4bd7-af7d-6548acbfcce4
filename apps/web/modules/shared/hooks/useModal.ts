'use client'

import { useAtom } from 'jotai'
import {
  showModalAtom,
  showLoginModalAtom,
  showInsufficientCreditsModalAtom,
  showConfirmModalAtom,
  showSubscriptionModalAtom,
  showMaintenanceModalAtom,
  hideCurrentModalAtom,
  clearAllModalsAtom,
  currentModalAtom,
  ModalConfig,
} from '@shared/stores'

/**
 * 全局弹窗管理 Hook
 * 提供便捷的弹窗操作方法
 */
export function useModal() {
  const [currentModal] = useAtom(currentModalAtom)
  const [, showModal] = useAtom(showModalAtom)
  const [, showLoginModal] = useAtom(showLoginModalAtom)
  const [, showInsufficientCreditsModal] = useAtom(showInsufficientCreditsModalAtom)
  const [, showConfirmModal] = useAtom(showConfirmModalAtom)
  const [, showSubscriptionModal] = useAtom(showSubscriptionModalAtom)
  const [, showMaintenanceModal] = useAtom(showMaintenanceModalAtom)
  const [, hideCurrentModal] = useAtom(hideCurrentModalAtom)
  const [, clearAllModals] = useAtom(clearAllModalsAtom)

  return {
    // 当前显示的弹窗
    currentModal,
    
    // 显示自定义弹窗
    showModal,
    
    // 显示登录弹窗
    showLoginModal: (config?: Partial<ModalConfig>) => {
      showLoginModal(config)
    },
    
    // 显示积分不足弹窗
    showInsufficientCreditsModal: (config?: Partial<ModalConfig>) => {
      showInsufficientCreditsModal(config)
    },
    
    // 显示确认弹窗
    showConfirmModal: (config: {
      title?: string
      content?: string
      onConfirm?: () => void
      onCancel?: () => void
      confirmText?: string
      cancelText?: string
      variant?: 'default' | 'destructive' | 'warning'
    }) => {
      showConfirmModal({
        title: config.title || '确认操作',
        content: config.content || '您确定要执行此操作吗？',
        onConfirm: config.onConfirm,
        onCancel: config.onCancel,
        confirmText: config.confirmText || '确认',
        cancelText: config.cancelText || '取消',
        props: {
          variant: config.variant || 'default'
        }
      })
    },
    
    // 显示删除确认弹窗（快捷方法）
    showDeleteConfirm: (onConfirm: () => void, itemName?: string) => {
      showConfirmModal({
        title: '确认删除',
        content: `您确定要删除${itemName ? ` "${itemName}"` : '此项'}吗？此操作无法撤销。`,
        onConfirm,
        confirmText: '删除',
        cancelText: '取消',
        props: {
          variant: 'destructive'
        }
      })
    },

    // 显示订阅弹窗
    showSubscriptionModal: (config?: Partial<ModalConfig>) => {
      showSubscriptionModal(config)
    },

    // 显示系统维护弹窗
    showMaintenanceModal: (config?: Partial<ModalConfig>) => {
      showMaintenanceModal(config)
    },
    
    // 关闭当前弹窗
    hideCurrentModal,
    
    // 清空所有弹窗
    clearAllModals,
    
    // 检查是否有弹窗正在显示
    isModalOpen: !!currentModal,
  }
}