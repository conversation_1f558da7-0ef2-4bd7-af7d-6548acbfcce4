/**
 * 积分配置数据
 * 纯配置文件，包含所有积分相关的配置数据
 */

// ========== 类型定义 ==========

export type ApiProvider = 'piapi' | 'kieai' | 'kling' | 'remaker' | 'suno'
export type MediaType = 'image' | 'video' | 'audio'
// 先定义配置对象，然后推断类型
const taskTypePointConfig = {
  'test-consume': {
    apiProvider: 'piapi',
    mediaType: 'image',
    pathname: '/ai/test-consume',
    description: '测试功能',
    pointRules: [
      {
        isDefault: true,
        points: 100,
        description: '标准测试功能',
      },
    ],
  },

  // PiAPI支持的任务类型
  'face-swap': {
    apiProvider: 'piapi',
    mediaType: 'image',
    pathname: '/ai/face-swap',
    description: '人脸替换',
    pointRules: [
      {
        isDefault: true,
        points: 8,
        description: '标准人脸替换',
      },
    ],
    endpoints: {
      generate: '/api/aiimage/generate',
      status: '/api/aiimage/task',
      history: '/api/history/getList',
    },
  },

  ai_hug: {
    apiProvider: 'piapi',
    mediaType: 'video',
    pathname: '/ai/ai-hug',
    description: 'AI拥抱视频生成',
    pointRules: [
      {
        isDefault: true,
        points: 12,
        description: '标准AI拥抱视频',
      },
    ],
    endpoints: {
      generate: '/api/aiimage/generate',
      status: '/api/aiimage/task',
      history: '/api/history/getList',
    },
  },

  upscale: {
    apiProvider: 'piapi',
    mediaType: 'image',
    pathname: '/ai/image-upscaler',
    description: '图像放大',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准图像放大',
      },
    ],
    endpoints: {
      generate: '/api/aiimage/generate',
      status: '/api/aiimage/task',
      history: '/api/history/getList',
    },
  },

  'background-remove': {
    apiProvider: 'piapi',
    mediaType: 'image',
    pathname: '/ai/background-removal',
    description: '背景移除',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准背景移除',
      },
    ],
    endpoints: {
      generate: '/api/aiimage/generate',
      status: '/api/aiimage/task',
      history: '/api/history/getList',
    },
  },

  // KieAI支持的任务类型
  aismile: {
    apiProvider: 'kieai',
    mediaType: 'video',
    pathname: '/ai/ai-smile',
    description: 'AI微笑视频生成',
    pointRules: [
      {
        isDefault: true,
        points: 12,
        description: '标准AI微笑视频',
        conditions: (params: any) => {
          return params.duration === 5 && params.quality === '720p'
        },
      },
      {
        isDefault: false,
        points: 15,
        description: '8秒或1080p视频',
        conditions: (params: any) => {
          return params.duration === 8 || params.quality === '1080p'
        },
      },
    ],
    endpoints: {
      generate: '/api/video/aismile',
      status: '/api/video/detail',
      history: '/api/history/getList',
    },
  },

  imagetovideo: {
    apiProvider: 'kieai',
    mediaType: 'video',
    pathname: '/ai/image-to-video',
    description: '图像转视频',
    pointRules: [
      {
        isDefault: true,
        points: 7,
        description: '5秒720p视频',
        conditions: (params: any) => {
          return params.duration === 5 && params.quality === '720p'
        },
      },
      {
        isDefault: false,
        points: 15,
        description: '8秒或1080p视频',
        conditions: (params: any) => {
          return params.duration === 8 || params.quality === '1080p'
        },
      },
    ],
    endpoints: {
      generate: '/api/video/imagetovideo',
      status: '/api/video/detail',
      history: '/api/history/getList',
    },
  },

  'photo-to-anime': {
    apiProvider: 'kieai',
    mediaType: 'image',
    pathname: '/ai/photo-to-anime',
    description: '照片转动漫风格',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准动漫风格转换',
      },
    ],
    endpoints: {
      generate: '/api/images/generate/photo-to-anime',
      status: '/api/images/record-info',
      history: '/api/history/getList',
    },
  },

  'photo-restoration': {
    apiProvider: 'kieai',
    mediaType: 'image',
    pathname: '/ai/photo-restoration',
    description: '照片修复',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准照片修复',
      },
    ],
    endpoints: {
      generate: '/api/images/generate/photo-restoration',
      status: '/api/images/record-info',
      history: '/api/history/getList',
    },
  },

  'old-filter': {
    apiProvider: 'kieai',
    mediaType: 'image',
    pathname: '/ai/old-filter',
    description: '老照片滤镜',
    pointRules: [
      {
        isDefault: true,
        points: 2,
        description: '标准老照片滤镜',
      },
    ],
    endpoints: {
      generate: '/api/images/generate/old-filter',
      status: '/api/images/record-info',
      history: '/api/history/getList',
    },
  },

  'text-to-image': {
    apiProvider: 'kieai',
    mediaType: 'image',
    pathname: '/ai/text-to-image',
    description: '文本生成图像',
    pointRules: [
      {
        isDefault: true,
        points: 6,
        description: '标准文本生成图像',
      },
    ],
    endpoints: {
      generate: '/api/images/generate',
      status: '/api/images/record-info',
      history: '/api/history/getList',
    },
  },

  'image-to-image': {
    apiProvider: 'kieai',
    mediaType: 'image',
    pathname: '/ai/image-to-image',
    description: '图像转图像',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准图像转换',
      },
    ],
    endpoints: {
      generate: '/api/images/generate',
      status: '/api/images/record-info',
      history: '/api/history/getList',
    },
  },

  ghibli: {
    apiProvider: 'kieai',
    mediaType: 'image',
    pathname: '/ai/ghibli',
    description: '吉卜力风格转换',
    pointRules: [
      {
        isDefault: true,
        points: 5,
        description: '标准吉卜力风格',
      },
    ],
    endpoints: {
      generate: '/api/images/generate',
      status: '/api/images/record-info',
      history: '/api/history/getList',
    },
  },

  'photo-colorizer': {
    apiProvider: 'kieai',
    mediaType: 'image',
    pathname: '/ai/photo-colorizer',
    description: '照片上色',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准照片上色',
      },
    ],
    endpoints: {
      generate: '/api/images/generate',
      status: '/api/images/record-info',
      history: '/api/history/getList',
    },
  },

  // Kling支持的任务类型
  ai_try_on: {
    apiProvider: 'kling',
    mediaType: 'image',
    pathname: '/ai/virtual-try-on',
    description: '虚拟试穿',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准虚拟试穿',
      },
    ],
    endpoints: {
      generate: '/api/kling/try-on',
      status: '/api/kling/try-on',
      history: '/api/history/getList',
    },
  },

  memory_video: {
    apiProvider: 'piapi',
    mediaType: 'video',
    pathname: '/ai/memory-video',
    description: '记忆视频生成',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准记忆视频生成',
      },
    ],
    endpoints: {
      generate: '/api/aiimage/generate',
      status: '/api/aiimage/task',
      history: '/api/history/getList',
    },
  },

  // 其他独立API
  tattoo: {
    apiProvider: 'remaker',
    mediaType: 'image',
    pathname: '/ai/tattoo',
    description: 'AI纹身生成',
    pointRules: [
      {
        isDefault: true,
        points: 3,
        description: '标准纹身生成',
      },
    ],
    endpoints: {
      generate: '/api/tattoo/create-job',
      status: '/api/tattoo/status',
    },
  },

  'sketch-to-image': {
    apiProvider: 'kieai',
    mediaType: 'image',
    pathname: '/ai/sketch-to-image',
    description: '草图转图像',
    pointRules: [
      {
        isDefault: true,
        points: 10,
        description: '标准草图转图像',
      },
    ],
    endpoints: {
      generate: '/api/images/generate',
      status: '/api/images/record-info',
      history: '/api/history/getList',
    },
  },
} as const

// 从配置对象推断出TaskType类型
export type TaskType = keyof typeof taskTypePointConfig

export interface PointCondition {
  isDefault: boolean
  points: number
  conditions?: (params: any) => boolean
  description?: string
}

export interface TaskTypePointConfig {
  apiProvider: ApiProvider
  mediaType: MediaType
  pathname: string
  description: string
  pointRules: PointCondition[]
  endpoints?: {
    generate: string
    status: string
    history?: string
  }
}

export interface PointCalculationParams {
  taskType: TaskType
  duration?: number
  quality?: string
  aspectRatio?: string
  batchSize?: number
  nVariants?: string | number
  [key: string]: any
}

export interface PointCalculationResult {
  points: number
  description: string
  rule: PointCondition
}

// ========== 导出 ==========

// 导出配置数据
export { taskTypePointConfig }
