'use client'

import { useAtom } from 'jotai'
import { themeAtom } from '@marketing/stores'
import { useEffect } from 'react'

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme] = useAtom(themeAtom)

  useEffect(() => {
    // 同步主题到 DOM
    if (typeof window !== 'undefined') {
      console.log('ThemeProvider: 应用主题到 DOM ->', theme)
      document.documentElement.classList.remove('light', 'dark')
      document.documentElement.classList.add(theme)

      // 确保主题已经应用
      const appliedTheme = document.documentElement.classList.contains(theme)
      console.log(
        'ThemeProvider: 主题应用结果 ->',
        appliedTheme ? '成功' : '失败'
      )
    }
  }, [theme])

  return <>{children}</>
}
