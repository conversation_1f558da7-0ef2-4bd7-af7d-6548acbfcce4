import { useTranslations } from 'next-intl'
import { Wand2, X } from 'lucide-react'
import { Link, usePathname } from '@i18n/routing'

interface Props {
  title: string
  desc: string
  needBottomArea?: boolean
  needBottomPay?: boolean
  onClose: () => void
}
export default function ShowLoginModal({
  title,
  desc,
  needBottomArea = false,
  needBottomPay = true,
  onClose,
}: Props) {
  const t = useTranslations()
  const pathname = usePathname()

  const handleJumpLogin = () => {
    localStorage.setItem('REDIRECT_PATH', pathname)
  }

  return (
    <div
      className="fixed inset-0 bg-black/30 backdrop-blur-sm z-50 flex items-center justify-center"
      onClick={onClose}
    >
      <div
        className="bg-white border border-gray-200 rounded-2xl p-8 max-w-md w-11/12 mx-4 relative transform transition-all shadow-lg"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-gray-500 hover:text-gray-800 transition-colors"
        >
          <X className="h-5 w-5" />
        </button>

        {/* 图标 */}
        <div className="h-20 w-20 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full mx-auto flex items-center justify-center mb-6">
          <Wand2 className="h-10 w-10 text-white" />
        </div>

        {/* 内容 */}
        <div className="text-center space-y-6">
          <h3 className="text-2xl font-bold">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-teal-600">
              {title}
            </span>
          </h3>

          <p className="text-lg font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-teal-500 animate-pulse">
            {desc}
          </p>

          <div className="space-y-2">
            <a
              href="/api/oauth/google"
              onClick={handleJumpLogin}
              className="block"
            >
              <button className="w-full bg-gradient-to-r from-blue-500 to-teal-500 text-white px-6 py-3 rounded-xl font-medium hover:opacity-90 transition-all">
                {t('freeTrail.loginNow')}
              </button>
            </a>
          </div>

          {needBottomArea && (
            <div className="space-y-3 pt-4">
              <h4 className="text-sm font-medium text-blue-600">
                {t('freeTrail.memberBenefits')}
              </h4>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-teal-500"></div>
                  {t('freeTrail.unlimitedGeneration')}
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-teal-500"></div>
                  {t('freeTrail.highQualityVoice')}
                </li>
                <li className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-teal-500"></div>
                  {t('freeTrail.batchExport')}
                </li>
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
