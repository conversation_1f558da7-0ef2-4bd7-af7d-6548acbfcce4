'use client'

import { cn } from '@ui/lib'
import { useState, useCallback, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Link, usePathname } from '@i18n/routing'
import { useTranslations } from 'next-intl'
import {
  ChevronRight,
  Camera,
  Image as ImageIcon,
  FileText,
  Settings,
  Zap,
  ChevronDown,
} from 'lucide-react'
import { getVideoMenuItems } from './Logo'
import { useAtom } from 'jotai'
import { themeAtom } from '@marketing/stores'

// 类型定义
interface MenuItem {
  title: string
  desc: string
  href: string
  seoHref?: string
  isHot?: boolean
  isNew?: boolean
  coming?: boolean
  preview?: string
  hidden?: boolean
  point?: number
  icon?: JSX.Element
  isVideo?: boolean
}

// 热门和新品标签组件
const HotLabel = () => {
  const t = useTranslations()
  return (
    <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-fuchsia-500 to-purple-600 px-2 py-0.5 text-xs font-medium text-white ml-1.5 shadow-sm">
      {t('common.menu.hot')}
    </span>
  )
}

const NewLabel = () => {
  const t = useTranslations()
  return (
    <span className="inline-flex items-center justify-center rounded-full bg-gradient-to-r from-violet-500 to-purple-600 px-2 py-0.5 text-xs font-medium text-white ml-1.5 shadow-sm">
      {t('common.menu.new')}
    </span>
  )
}

const ComingLabel = () => {
  const t = useTranslations()
  return (
    <span className="inline-flex items-center justify-center bg-orange-800/60 text-orange-300 px-2 py-0.5 text-xs font-medium rounded ml-1.5 whitespace-nowrap border border-orange-500/20">
      {t('common.menu.coming')}
    </span>
  )
}

interface VideoDropdownMenuProps {
  children: React.ReactNode
  className?: string
}

export function VideoDropdownMenu({
  children,
  className,
}: VideoDropdownMenuProps) {
  const [isHovered, setIsHovered] = useState(false)
  const pathname = usePathname()
  const router = useRouter()
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const t = useTranslations()
  const [theme] = useAtom(themeAtom)
  const useCaseItems = [
    {
      dynamicImage: '/gif/memorial-video-maker-after.gif',
      staticImage:
        '/samples/memorial-video-maker/memorial-video-maker-before.png',
      title: t('common.menu.memoryVideos'),
      desc: t('common.menu.memoryVideosDesc'),
      href: '/tools/memory-video',
      alt: t('common.menu.memoryVideoDemo'),
      btnText: t('common.menu.createMemoryVideo'),
      btnClassName: 'bg-indigo-600 hover:bg-indigo-700',
    },
    {
      dynamicImage:
        'https://res.cloudinary.com/dnwsyyrpt/image/upload/v1751803083/282911280527853_msmoqf.gif',
      staticImage:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/E2.png',
      title: t('common.menu.aiHugVideos'),
      desc: t('common.menu.aiHugVideosDesc'),
      href: '/tools/ai-hug',
      alt: t('common.menu.aiHugVideoDemo'),
      btnText: t('common.menu.tryAiHug'),
      btnClassName: 'bg-pink-600 hover:bg-pink-700',
    },
    {
      dynamicImage:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/C2.mp4',
      staticImage:
        'https://aimakesong-gpt4o-image.oss-ap-southeast-1.aliyuncs.com/photo-to-video/C1.jpeg',
      title: t('tools.imageTools.items.imageToVideo.title'),
      desc: t('photoToVideo.heroDescription'),
      href: '/tools/photo-to-video',
      alt: t('common.menu.aiImageToVideoDemo'),
      btnText: t('common.menu.imageToVideo'),
      btnClassName: 'bg-purple-600 hover:bg-purple-700',
      isVideo: true,
    },
  ]

  // 检查是否在 AI 页面
  const isAiPage = pathname.startsWith('/ai/')

  // 获取视频相关菜单项
  const videoItems = getVideoMenuItems(isAiPage, t)

  // 导航处理函数
  const handleNavigation = useCallback(() => {
    setIsHovered(false)
  }, [])

  // 处理鼠标进入事件
  const handleMouseEnter = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
      timeoutRef.current = null
    }
    setIsHovered(true)
  }, [])

  // 处理鼠标离开事件
  const handleMouseLeave = useCallback(() => {
    timeoutRef.current = setTimeout(() => {
      setIsHovered(false)
    }, 150)
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  // 显示所有视频工具（包括 coming 和 hidden 的项目）
  const availableVideoItems = videoItems

  // 获取前4个作为特色工具
  const featuredItems = availableVideoItems.slice(0, 4)
  const remainingItems = availableVideoItems.slice(4)

  return (
    <div
      className={cn('relative', className)}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}

      {/* Dropdown Menu */}
      <div
        className={cn(
          'absolute max-h-[calc(100vh-62px)] overscroll-y-contain !overflow-y-auto top-[calc(100%+8px)] left-1/2 backdrop-blur-md rounded-xl overflow-hidden z-[200]',
          'w-[1100px] max-w-[95vw]',
          ' origin-top',
          theme === 'light'
            ? 'bg-white border border-blue-200/50'
            : 'bg-gray-900/95 border border-purple-500/20',
          isHovered
            ? theme === 'light'
              ? 'animate-dropdown opacity-100 visible -translate-x-1/3 translate-y-0 shadow-[0_4px_20px_-2px_rgba(0,0,0,0.1),0_0_15px_-3px_rgba(59,130,246,0.2)] border-blue-200/60'
              : 'animate-dropdown opacity-100 visible -translate-x-1/3 translate-y-0 shadow-[0_4px_20px_-2px_rgba(0,0,0,0.3),0_0_15px_-3px_rgba(127,50,237,0.3)] border-purple-500/30'
            : ' opacity-0 invisible -translate-x-1/3 translate-y-[-8px] shadow-none pointer-events-none'
        )}
        style={{ '--translate-x': '-33.33%' } as React.CSSProperties}
      >
        <div className="p-8">
          {/* AI Video Tools Section */}
          <div className="mb-8">
            <h2
              className={cn(
                'text-xl font-semibold mb-6',
                theme === 'light' ? 'text-gray-900' : 'text-white'
              )}
            >
              {t('common.menu.aiVideoTools')}
            </h2>

            {/* Use Cases Section */}
            <div className="mb-8">
              <h3
                className={cn(
                  'text-lg font-medium mb-4',
                  theme === 'light' ? 'text-gray-600' : 'text-gray-300'
                )}
              >
                {t('common.menu.popularUseCases')}
              </h3>
              <div className="grid grid-cols-3 gap-6">
                {useCaseItems.map((item, index) => {
                  return (
                    <div
                      key={`${item.title}-${index}}`}
                      className={cn(
                        'rounded-xl p-4 border transition-all',
                        theme === 'light'
                          ? 'bg-gray-50/80 border-gray-200/60 hover:border-blue-300/50'
                          : 'bg-gray-800/50 border-gray-700/50 hover:border-purple-500/30'
                      )}
                    >
                      <div
                        className={cn(
                          'aspect-video relative rounded-lg mb-3 overflow-hidden group',
                          theme === 'light' ? 'bg-gray-200' : 'bg-gray-700'
                        )}
                      >
                        {item.isVideo && (
                          <video
                            muted
                            autoPlay
                            loop
                            className={`w-full h-full object-cover rounded-lg ${
                              item.isVideo ? '' : 'hidden'
                            }`}
                            src={item.dynamicImage}
                          ></video>
                        )}
                        <img
                          className={`w-full h-full object-cover rounded-lg ${
                            item.isVideo ? 'hidden' : ''
                          }`}
                          src={item.dynamicImage}
                          alt={item.alt}
                          loading="lazy"
                        />
                        {/* Hover overlay */}
                        <div className="absolute inset-0 bg-black/10 group-hover:bg-black/5 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                          <div className="bg-white/20 backdrop-blur-sm rounded-full p-3">
                            <svg
                              className="w-6 h-6 text-white"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path d="M8 5v14l11-7z" />
                            </svg>
                          </div>
                        </div>
                        <div className="absolute bottom-1 left-1 w-14 h-14 rounded-lg overflow-hidden border-2 border-white/70 shadow-xl">
                          <img
                            src={item.staticImage}
                            alt={`${item.alt}`}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                            <span className="text-white text-xs font-semibold">
                              {t('oldfilter.compareBefore')}
                            </span>
                          </div>
                        </div>
                      </div>
                      <h4
                        className={cn(
                          'font-medium mb-2',
                          theme === 'light' ? 'text-gray-900' : 'text-white'
                        )}
                      >
                        {item.title}
                      </h4>
                      <p
                        className={cn(
                          'text-sm mb-3 line-clamp-3',
                          theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                        )}
                      >
                        {item.desc}
                      </p>
                      <Link
                        href={item.href || ''}
                        onClick={() => handleNavigation()}
                      >
                        <button
                          className={`w-full text-white text-sm py-2 rounded-lg transition-colors ${item.btnClassName}`}
                        >
                          {item.btnText}
                        </button>
                      </Link>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Featured Video Tools - up to 4 cards in a row */}
            {featuredItems.length > 0 && (
              <div
                className={`grid ${
                  featuredItems.length === 1
                    ? 'grid-cols-1'
                    : featuredItems.length === 2
                    ? 'grid-cols-2'
                    : featuredItems.length === 3
                    ? 'grid-cols-3'
                    : 'grid-cols-4'
                } gap-4 mb-6`}
              >
                {featuredItems.map((item, index) => (
                  <Link
                    href={item.href}
                    key={index}
                    onClick={() => !item.coming && handleNavigation()}
                    className={cn(
                      'group relative rounded-xl p-4 transition-all',
                      theme === 'light'
                        ? 'bg-gradient-to-r from-blue-500 to-indigo-600'
                        : 'bg-gradient-to-r from-purple-600 to-pink-600',
                      item.coming
                        ? 'opacity-60 !cursor-not-allowed pointer-events-none'
                        : theme === 'light'
                        ? 'cursor-pointer hover:shadow-lg hover:shadow-blue-500/20'
                        : 'cursor-pointer hover:shadow-lg hover:shadow-purple-500/20'
                    )}
                  >
                    <div className="absolute top-3 left-3">
                      <span
                        className={cn(
                          'text-white text-xs px-2 py-1 rounded-full font-medium',
                          theme === 'light' ? 'bg-white/20' : 'bg-blue-500'
                        )}
                      >
                        {t('common.menu.free')}
                      </span>
                    </div>
                    {item.isHot && (
                      <div className="absolute top-3 right-3">
                        <HotLabel />
                      </div>
                    )}
                    {item.isNew && (
                      <div className="absolute top-3 right-3">
                        <NewLabel />
                      </div>
                    )}
                    {item.coming && (
                      <div className="absolute top-3 right-3">
                        <ComingLabel />
                      </div>
                    )}
                    <div className="pt-8">
                      <h3 className="text-white font-medium text-lg mb-2">
                        {item.title}
                      </h3>
                      <p className="text-white/80 text-sm">{item.desc}</p>
                    </div>
                  </Link>
                ))}
              </div>
            )}
          </div>

          {/* Additional Video Tools Section */}
          {remainingItems.length > 0 && (
            <div>
              <h2
                className={cn(
                  'text-xl font-semibold mb-6',
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                )}
              >
                {t('common.menu.moreVideoTools')}
              </h2>

              {/* Grid of additional tools */}
              <div className="grid grid-cols-4 gap-4">
                {remainingItems.map((item, index) => (
                  <Link
                    href={item.href}
                    key={index}
                    onClick={() => {
                      if (item.coming) return
                      handleNavigation()
                    }}
                    className={cn(
                      'group flex relative items-center gap-3 p-4 rounded-lg transition-all',
                      item.coming
                        ? 'opacity-60 cursor-not-allowed !pointer-events-none'
                        : theme === 'light'
                        ? 'cursor-pointer hover:bg-gray-100/60'
                        : 'cursor-pointer hover:bg-gray-800/60'
                    )}
                  >
                    <div
                      className={cn(
                        'w-10 h-10 rounded-lg flex items-center justify-center',
                        theme === 'light' ? 'bg-gray-200' : 'bg-gray-800'
                      )}
                    >
                      {item.icon || (
                        <Camera
                          className={cn(
                            'w-5 h-5',
                            theme === 'light'
                              ? 'text-blue-500'
                              : 'text-purple-400'
                          )}
                        />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3
                          className={cn(
                            'font-medium text-sm',
                            theme === 'light' ? 'text-gray-900' : 'text-white'
                          )}
                        >
                          {item.title}
                        </h3>

                        {item.isHot && <HotLabel />}
                        {item.isNew && <NewLabel />}
                        {/* {item.coming && <ComingLabel />} */}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* No tools available message */}
          {availableVideoItems.length === 0 && (
            <div className="text-center py-8">
              <Camera
                className={cn(
                  'w-16 h-16 mx-auto mb-4',
                  theme === 'light' ? 'text-gray-400' : 'text-gray-500'
                )}
              />
              <h3
                className={cn(
                  'text-lg font-medium mb-2',
                  theme === 'light' ? 'text-gray-900' : 'text-white'
                )}
              >
                {t('common.menu.noVideoToolsAvailable')}
              </h3>
              <p
                className={cn(
                  theme === 'light' ? 'text-gray-600' : 'text-gray-400'
                )}
              >
                {t('common.menu.videoToolsComingSoon')}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
