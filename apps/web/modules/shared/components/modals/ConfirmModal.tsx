'use client'

import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogFooter,
} from '@ui/components/dialog'
import { Button } from '@ui/components/button'
import { AlertTriangle } from 'lucide-react'

interface ConfirmModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  title?: string
  content?: string
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
  variant?: 'default' | 'destructive' | 'warning'
}

export function ConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  title = '确认操作',
  content = '您确定要执行此操作吗？',
  confirmText = '确认',
  cancelText = '取消',
  showCancel = true,
  variant = 'default',
}: ConfirmModalProps) {
  const getVariantStyles = () => {
    switch (variant) {
      case 'destructive':
        return {
          iconColor: 'text-red-500',
          iconBg: 'bg-red-100',
          titleColor: 'text-red-700',
          confirmVariant: 'destructive' as const,
        }
      case 'warning':
        return {
          iconColor: 'text-yellow-500',
          iconBg: 'bg-yellow-100',
          titleColor: 'text-yellow-700',
          confirmVariant: 'default' as const,
        }
      default:
        return {
          iconColor: 'text-blue-500',
          iconBg: 'bg-blue-100',
          titleColor: 'text-gray-900',
          confirmVariant: 'default' as const,
        }
    }
  }

  const styles = getVariantStyles()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${styles.iconBg}`}>
              <AlertTriangle className={`h-5 w-5 ${styles.iconColor}`} />
            </div>
            <DialogTitle className={styles.titleColor}>
              {title}
            </DialogTitle>
          </div>
        </DialogHeader>
        
        <div className="py-4">
          <p className="text-gray-600 text-sm leading-relaxed">
            {content}
          </p>
        </div>

        <DialogFooter className="flex space-x-2">
          {showCancel && (
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              {cancelText}
            </Button>
          )}
          <Button
            variant={styles.confirmVariant}
            onClick={onConfirm}
            className="flex-1"
          >
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}