'use client'

import { useTranslations } from 'next-intl'
import { X, Mail } from 'lucide-react'
import { usePathname } from '@i18n/routing'
import Image from 'next/image'

interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  content?: string
  needBottomArea?: boolean
  needBottomPay?: boolean
}

export function LoginModal({
  isOpen,
  onClose,
  title,
  content,
  needBottomArea = false,
  needBottomPay = true,
}: LoginModalProps) {
  const t = useTranslations()
  const pathname = usePathname()

  const handleJumpLogin = () => {
    localStorage.setItem('REDIRECT_PATH', pathname)
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <div
        className="bg-white rounded-3xl max-w-4xl w-full h-[600px] relative transform transition-all shadow-2xl overflow-hidden flex"
        onClick={(e) => e.stopPropagation()}
      >
        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute top-6 right-6 z-20 text-gray-500 hover:text-gray-700 transition-colors bg-white/90 rounded-full p-2 shadow-lg backdrop-blur-sm"
        >
          <X className="h-5 w-5" />
        </button>

        {/* 左侧 - 品牌展示区域 */}
        <div className="flex-1 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 relative overflow-hidden flex items-center justify-center">
          {/* 极光背景效果 */}
          <div className="absolute inset-0">
            {/* 主要极光层 */}
            <div className="absolute inset-0 bg-gradient-to-br from-purple-600/40 via-blue-500/30 to-teal-400/40"></div>
            <div className="absolute inset-0 bg-gradient-to-t from-purple-900/60 via-transparent to-blue-900/40"></div>
            <div className="absolute inset-0 bg-gradient-to-bl from-pink-500/20 via-transparent to-purple-600/30"></div>

            {/* 动态光效 */}
            <div className="absolute top-1/4 left-0 w-full h-32 bg-gradient-to-r from-transparent via-purple-400/20 to-transparent transform -skew-y-12 animate-pulse"></div>
            <div
              className="absolute top-1/2 left-0 w-full h-24 bg-gradient-to-r from-transparent via-pink-400/15 to-transparent transform skew-y-6 animate-pulse"
              style={{ animationDelay: '1.5s' }}
            ></div>
            <div
              className="absolute bottom-1/4 left-0 w-full h-20 bg-gradient-to-r from-transparent via-blue-400/20 to-transparent transform -skew-y-3 animate-pulse"
              style={{ animationDelay: '3s' }}
            ></div>
          </div>

          {/* 浮动光点 */}
          <div className="absolute top-16 left-16 w-2 h-2 bg-white/60 rounded-full animate-pulse"></div>
          <div
            className="absolute top-32 right-24 w-1 h-1 bg-purple-300/80 rounded-full animate-pulse"
            style={{ animationDelay: '0.5s' }}
          ></div>
          <div
            className="absolute bottom-32 left-32 w-1.5 h-1.5 bg-pink-300/70 rounded-full animate-pulse"
            style={{ animationDelay: '2s' }}
          ></div>
          <div
            className="absolute bottom-16 right-16 w-1 h-1 bg-blue-300/60 rounded-full animate-pulse"
            style={{ animationDelay: '1s' }}
          ></div>

          {/* 品牌LOGO和标语 */}
          <div className="relative z-10 text-center">
            {/* LOGO图标 */}
            <div className="mb-8">
              <div className="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-3xl flex items-center justify-center shadow-2xl transform hover:scale-105 transition-transform duration-300">
                <span className="text-white font-bold text-3xl">I</span>
              </div>
            </div>

            {/* 品牌名称 */}
            <h1 className="text-6xl font-bold text-white mb-6 tracking-wide drop-shadow-lg">
              removeAI
            </h1>

            {/* 标语 */}
            <p className="text-xl text-purple-100 font-light tracking-wider opacity-90">
              Visualize Your Creativity
            </p>
          </div>

          {/* 边缘光效 */}
          <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-purple-400/60 to-transparent"></div>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-pink-400/60 to-transparent"></div>
        </div>

        {/* 右侧 - 登录区域 */}
        <div className="flex-1 p-12 flex flex-col justify-center bg-white">
          <div className="max-w-sm mx-auto w-full">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {title || 'Welcome Back'}
              </h1>
              <p className="text-gray-600">
                {content || 'Sign in to continue your creative journey'}
              </p>
            </div>

            {/* 登录按钮 */}
            <div className="space-y-4">
              {/* Google 登录 */}
              <a
                href="/api/oauth/google"
                onClick={handleJumpLogin}
                className="block"
              >
                <button className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-6 py-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
                  <svg className="w-5 h-5" viewBox="0 0 24 24">
                    <path
                      fill="currentColor"
                      d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                    />
                    <path
                      fill="currentColor"
                      d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                    />
                    <path
                      fill="currentColor"
                      d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                    />
                  </svg>
                  <span>Continue with Google</span>
                </button>
              </a>

              {/* 分割线 */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">or</span>
                </div>
              </div>

              {/* Email 登录 */}
              <button className="w-full bg-white hover:bg-purple-50 text-gray-700 hover:text-purple-700 px-6 py-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-3 border-2 border-purple-200 hover:border-purple-300">
                <Mail className="w-5 h-5" />
                <span>Continue with email</span>
              </button>
            </div>

            {/* 底部说明 */}
            {/* <div className="mt-8 text-center">
              <p className="text-xs text-gray-500 leading-relaxed">
                By signing up, you agree to our{' '}
                <a href="#" className="underline hover:text-gray-700">Terms of Use</a>{' '}
                and{' '}
                <a href="#" className="underline hover:text-gray-700">Privacy Policy</a>
              </p>
            </div> */}

            {/* {needBottomArea && (
              <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl border border-purple-200">
                <h4 className="text-sm font-medium text-purple-800 mb-2">
                  ✨ Member Benefits
                </h4>
                <ul className="text-xs text-purple-600 space-y-1">
                  <li>• Unlimited AI image generation</li>
                  <li>• High-quality outputs</li>
                  <li>• Priority processing</li>
                  <li>• Advanced customization</li>
                </ul>
              </div>
            )} */}
          </div>
        </div>
      </div>
    </div>
  )
}
