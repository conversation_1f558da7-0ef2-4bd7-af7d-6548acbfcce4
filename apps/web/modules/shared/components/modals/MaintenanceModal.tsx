'use client'

import { useTranslations } from 'next-intl'
import { AlertTriangle, Mail, MessageCircle } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'

interface MaintenanceModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  content?: string
}

export function MaintenanceModal({
  isOpen,
  onClose,
  title,
  content,
}: MaintenanceModalProps) {
  const t = useTranslations()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="
          max-w-[500px]
          w-full
          bg-white
          border-gray-200
          shadow-xl
          rounded-3xl
        "
      >
        <DialogHeader>
          <DialogTitle className="text-center text-gray-900 flex items-center justify-center gap-3">
            <AlertTriangle className="h-6 w-6 text-orange-500" />
            {title || '系统维护'}
          </DialogTitle>
        </DialogHeader>
        
        <div className="px-6 pb-6">
          <div className="text-center space-y-6">
            {/* 维护图标 */}
            <div className="flex justify-center">
              <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center">
                <AlertTriangle className="h-10 w-10 text-orange-500" />
              </div>
            </div>

            {/* 维护信息 */}
            <div className="space-y-3">
              <p className="text-lg font-medium text-gray-900">
                {content || '系统正在维护中'}
              </p>
              <p className="text-gray-600">
                抱歉给您带来不便，请联系管理员或稍后再试
              </p>
            </div>

            {/* 联系方式 */}
            <div className="bg-gray-50 rounded-xl p-4 space-y-3">
              <h4 className="font-medium text-gray-900">联系我们</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-center gap-2 text-gray-600">
                  <Mail className="h-4 w-4" />
                  <span className="text-sm"><EMAIL></span>
                </div>
                <div className="flex items-center justify-center gap-2 text-gray-600">
                  <MessageCircle className="h-4 w-4" />
                  <span className="text-sm">在线客服</span>
                </div>
              </div>
            </div>

            {/* 确认按钮 */}
            <button
              onClick={onClose}
              className="
                w-full
                bg-orange-500
                hover:bg-orange-600
                text-white
                px-6
                py-3
                rounded-xl
                font-medium
                transition-colors
                duration-200
              "
            >
              我知道了
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}