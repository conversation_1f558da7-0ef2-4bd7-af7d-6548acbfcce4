'use client'

import { useTranslations } from 'next-intl'
import { X } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@ui/components/dialog'
import PricingSection from '@/[locale]/(marketing)/(home)/components/PricingSection'

interface SubscriptionModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  content?: string
  message?: string
  showInsufficientCredits?: boolean
}

export function SubscriptionModal({
  isOpen,
  onClose,
  title,
  content,
  message,
  showInsufficientCredits = false,
}: SubscriptionModalProps) {
  const t = useTranslations()

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="
          max-w-[1400px]
          w-[90vw]
          h-[90vh]
          bg-[#1A1B1E]
          border-[#2D2E32]
          shadow-xl
        "
      >
        <button
          onClick={onClose}
          className="absolute right-4 top-4 z-50 rounded-full p-2 text-gray-400 hover:text-white hover:bg-gray-700/50 transition-colors"
          aria-label="Close modal"
        >
          <X className="h-5 w-5" />
        </button>

        <DialogHeader>
          <DialogTitle className="text-white">
            {title || t('upgradeToMember')}
          </DialogTitle>
        </DialogHeader>

        {showInsufficientCredits && message && (
          <div className="w-full bg-red-500/10 border border-red-500/20 rounded-xl p-4 mb-4">
            <p className="text-center font-semibold text-xl text-purple-300">
              <span className="text-red-500">{message}</span>
            </p>
          </div>
        )}

        {/* <div className="w-full ">
          <p className="text-center font-semibold text-lg text-purple-300">
            {content || '升级为会员以享受更多功能和权益'}
          </p>
        </div> */}

        <PricingSection
          needTitle={false}
          theme="dark"
          className="leading-none"
        />
      </DialogContent>
    </Dialog>
  )
}
