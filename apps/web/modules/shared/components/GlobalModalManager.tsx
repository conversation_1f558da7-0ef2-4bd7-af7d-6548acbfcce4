'use client'

import { useAtom } from 'jotai'
import { currentModalAtom, hideCurrentModalAtom } from '@shared/stores'
import { LoginModal } from './modals/LoginModal'
import { InsufficientCreditsModal } from './modals/InsufficientCreditsModal'
import { ConfirmModal } from './modals/ConfirmModal'
import { CustomModal } from './modals/CustomModal'
import { SubscriptionModal } from './modals/SubscriptionModal'
import { MaintenanceModal } from './modals/MaintenanceModal'
import { MenuModalComponent } from './MenuModal/MenuModalComponent'

/**
 * 全局弹窗管理器
 * 应该放在应用的根布局中，用于统一管理所有弹窗
 */
export function GlobalModalManager() {
  const [currentModal] = useAtom(currentModalAtom)
  const [, hideModal] = useAtom(hideCurrentModalAtom)

  if (!currentModal) {
    return null
  }

  const handleClose = () => {
    // 调用取消回调（如果有）
    if (currentModal.onCancel) {
      currentModal.onCancel()
    }
    hideModal()
  }

  const handleConfirm = () => {
    // 调用确认回调（如果有）
    if (currentModal.onConfirm) {
      currentModal.onConfirm()
    }
    hideModal()
  }

  // 根据弹窗类型渲染不同的弹窗组件
  switch (currentModal.type) {
    case 'login':
      return (
        <LoginModal
          isOpen={true}
          onClose={handleClose}
          title={currentModal.title}
          content={currentModal.content}
          {...(currentModal.props || {})}
        />
      )

    case 'insufficient-credits':
      return (
        <InsufficientCreditsModal
          isOpen={true}
          onClose={handleClose}
          message={currentModal.content}
          {...(currentModal.props || {})}
        />
      )

    case 'confirm':
      return (
        <ConfirmModal
          isOpen={true}
          onClose={handleClose}
          onConfirm={handleConfirm}
          title={currentModal.title}
          content={currentModal.content}
          confirmText={currentModal.confirmText}
          cancelText={currentModal.cancelText}
          showCancel={currentModal.showCancel}
          {...(currentModal.props || {})}
        />
      )

    case 'subscription':
      return (
        <SubscriptionModal
          isOpen={true}
          onClose={handleClose}
          title={currentModal.title}
          content={currentModal.content}
          {...(currentModal.props || {})}
        />
      )

    case 'maintenance':
      return (
        <MaintenanceModal
          isOpen={true}
          onClose={handleClose}
          title={currentModal.title}
          content={currentModal.content}
          {...(currentModal.props || {})}
        />
      )

    case 'custom':
      return (
        <CustomModal
          isOpen={true}
          onClose={handleClose}
          onConfirm={handleConfirm}
          config={currentModal}
          {...(currentModal.props || {})}
        />
      )

    case 'menu':
      return (
        <MenuModalComponent
          isOpen={true}
          itemIds={currentModal.itemIds || currentModal.categoryId || []}
          onClose={handleClose}
          title={currentModal.title}
          viewMoreHref={currentModal.viewMoreHref}
          {...(currentModal.props || {})}
        />
      )

    default:
      return null
  }
}
