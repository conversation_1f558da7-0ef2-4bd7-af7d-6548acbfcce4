import { atom } from 'jotai'
import type { CategoryId, ItemId } from '@shared/hooks/useMenuModal'
import { getMenuCategories } from '@shared/components/Logo'

// 弹窗类型定义
export interface ModalConfig {
  id: string
  type:
    | 'login'
    | 'insufficient-credits'
    | 'confirm'
    | 'custom'
    | 'subscription'
    | 'maintenance'
    | 'menu'
  title?: string
  content?: string
  props?: Record<string, any>
  onConfirm?: () => void
  onCancel?: () => void
  showCancel?: boolean
  confirmText?: string
  cancelText?: string
  // menu模态框特有属性
  categoryId?: CategoryId
  // 按项目ID筛选的菜单模态框特有属性
  itemIds?: ItemId[] | ItemId | CategoryId
  // 自定义view more按钮链接
  viewMoreHref?: string
}

// 全局弹窗队列状态
export const modalQueueAtom = atom<ModalConfig[]>([])

// 当前显示的弹窗
export const currentModalAtom = atom<ModalConfig | null>((get) => {
  const queue = get(modalQueueAtom)
  return queue[0] || null
})

// 显示弹窗的 action
export const showModalAtom = atom(null, (get, set, modal: ModalConfig) => {
  const queue = get(modalQueueAtom)
  set(modalQueueAtom, [...queue, modal])
})

// 关闭当前弹窗的 action
export const hideCurrentModalAtom = atom(null, (get, set) => {
  const queue = get(modalQueueAtom)
  if (queue.length > 0) {
    set(modalQueueAtom, queue.slice(1))
  }
})

// 清空所有弹窗的 action
export const clearAllModalsAtom = atom(null, (get, set) => {
  set(modalQueueAtom, [])
})

// 便捷方法：显示登录弹窗
export const showLoginModalAtom = atom(
  null,
  (get, set, config?: Partial<ModalConfig>) => {
    const loginModal: ModalConfig = {
      id: 'login-modal',
      type: 'login',
      title: config?.title || '需要登录',
      content: config?.content || '请先登录以继续使用',
      ...config,
    }
    set(showModalAtom, loginModal)
  }
)

// 便捷方法：显示积分不足弹窗
export const showInsufficientCreditsModalAtom = atom(
  null,
  (get, set, config?: Partial<ModalConfig>) => {
    const creditsModal: ModalConfig = {
      id: 'insufficient-credits-modal',
      type: 'insufficient-credits',
      title: config?.title || '积分不足',
      content: config?.content || '当前积分不足，请充值后继续',
      ...config,
    }
    set(showModalAtom, creditsModal)
  }
)

// 便捷方法：显示确认弹窗
export const showConfirmModalAtom = atom(
  null,
  (get, set, config: Omit<ModalConfig, 'id' | 'type'>) => {
    const confirmModal: ModalConfig = {
      id: `confirm-modal-${Date.now()}`,
      type: 'confirm',
      showCancel: true,
      confirmText: '确认',
      cancelText: '取消',
      ...config,
    }
    set(showModalAtom, confirmModal)
  }
)

// 便捷方法：显示订阅弹窗
export const showSubscriptionModalAtom = atom(
  null,
  (get, set, config?: Partial<ModalConfig>) => {
    const subscriptionModal: ModalConfig = {
      id: 'subscription-modal',
      type: 'subscription',
      title: config?.title || '升级会员',
      content: config?.content || '升级为会员以享受更多功能和权益',
      ...config,
    }
    set(showModalAtom, subscriptionModal)
  }
)

// 便捷方法：显示系统维护弹窗
export const showMaintenanceModalAtom = atom(
  null,
  (get, set, config?: Partial<ModalConfig>) => {
    const maintenanceModal: ModalConfig = {
      id: 'maintenance-modal',
      type: 'maintenance',
      title: config?.title || '系统维护',
      content: config?.content || '系统正在维护中，请联系管理员或稍后再试',
      ...config,
    }
    set(showModalAtom, maintenanceModal)
  }
)

// 便捷方法：显示按项目ID筛选的菜单弹窗
export const showMenuModalByItemIdsAtom = atom(
  null,
  (get, set, itemIds: ItemId[] | ItemId | keyof ReturnType<typeof getMenuCategories>, config?: Partial<ModalConfig>) => {
    const menuByItemIdsModal: ModalConfig = {
      id: `menu-modal-${
        typeof itemIds === 'string' ? itemIds : itemIds.join('-')
      }`,
      type: 'menu',
      itemIds,
      ...config,
    }
    set(showModalAtom, menuByItemIdsModal)
  }
)
