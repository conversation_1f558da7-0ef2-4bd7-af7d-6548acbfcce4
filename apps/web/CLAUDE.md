# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Package Management
This is a monorepo using pnpm workspaces. Run commands from the web app directory (`apps/web/`):

- `pnpm dev` - Start development server with Turbo
- `pnpm build` - Build the application
- `pnpm start` - Start production server
- `pnpm lint` - Run Next.js linting
- `pnpm type-check` - Run TypeScript type checking

### Testing
- `pnpm e2e` - Run Cypress E2E tests (opens Cypress UI)

## Architecture Overview

### Framework & Stack
- **Next.js 15** with App Router and React 19 RC
- **TypeScript** with strict configuration
- **Tailwind CSS** with custom Ghibli-inspired design system
- **tRPC** for type-safe API routes
- **Supabase** for database and authentication
- **Jotai** for state management
- **React Hook Form** + Zod for form validation

### Project Structure

#### Core Directories
- `app/` - Next.js App Router with internationalized routes
  - `[locale]/` - Localized routes (marketing, saas, seo)
  - `api/` - API routes for various services
- `modules/` - Feature-based modules
  - `marketing/` - Marketing components and pages
  - `saas/` - SaaS application components
  - `shared/` - Shared components and utilities
  - `ui/` - shadcn/ui components and design system
- `content/` - MDX content for documentation and legal pages
- `public/` - Static assets including AI samples and media

#### Key Features
- **Multi-tenant SaaS** with team management
- **AI Image Generation** with multiple providers (face-swap, image-to-image, etc.)
- **Internationalization** with next-intl
- **Authentication** with OAuth (Google, GitHub) and magic links
- **Payment Integration** with Stripe, LemonSqueezy, and Chargebee
- **Content Management** with content-collections for MDX

### Configuration Files
- `next.config.ts` - Next.js configuration with content collections, i18n, and image optimization
- `tailwind.config.ts` - Custom Ghibli-themed design system with Studio Ghibli-inspired colors
- `biome.json` - Code formatting (prefers Biome over Prettier/ESLint)
- `middleware.ts` - Handles internationalization routing

### Database Schema
The app uses Supabase with a primary table `image_generation_history` for tracking AI-generated images:
- Stores user generations with original/generated URLs
- Tracks prompts, ratios, and metadata
- Soft delete functionality with `is_deleted` flag

### AI Integration
Multiple AI service adapters in `app/[locale]/(marketing)/ai/components/adapters/`:
- Factory pattern for different AI providers
- Unified generation hooks and history management
- Support for face-swap, image-to-image, video generation, etc.

### Global Modal System
A comprehensive modal management system using Jotai for state management:

#### Components Location
- `modules/shared/stores/modalAtoms.ts` - Global modal state management
- `modules/shared/components/GlobalModalManager.tsx` - Central modal renderer
- `modules/shared/components/modals/` - Individual modal components
- `modules/shared/hooks/useModal.ts` - Convenient hook for modal operations

#### Usage Examples
```tsx
import { useModal } from '@shared/hooks/useModal'

function MyComponent() {
  const { showLoginModal, showConfirmModal, showDeleteConfirm } = useModal()
  
  // Show login modal
  const handleLogin = () => {
    showLoginModal({ 
      title: 'Please Login', 
      content: 'Login to continue' 
    })
  }
  
  // Show confirmation dialog
  const handleConfirm = () => {
    showConfirmModal({
      title: 'Confirm Action',
      content: 'Are you sure?',
      onConfirm: () => console.log('confirmed'),
      variant: 'destructive'
    })
  }
  
  // Quick delete confirmation
  const handleDelete = () => {
    showDeleteConfirm(() => deleteItem(), 'item name')
  }
}
```

### Development Notes
- Uses workspace packages (`api`, `database`, `auth`, `utils`, etc.)
- Monospace font rendering with CommonMark for documentation
- Cypress E2E testing setup
- Build errors and lint errors are ignored in production builds
- External `@node-rs/argon2` for password hashing
- Global modal system integrated in root layout with `GlobalModalManager`