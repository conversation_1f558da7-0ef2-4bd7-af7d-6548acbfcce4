{"extends": "tsconfig/nextjs.json", "baseUrl": ".", "compilerOptions": {"plugins": [{"name": "next"}], "jsx": "preserve", "paths": {"@/*": ["./app/*"], "@config": ["../../config"], "@analytics": ["./modules/analytics"], "@marketing/*": ["./modules/marketing/*"], "@saas/*": ["./modules/saas/*"], "@ui/*": ["./modules/ui/*"], "@i18n": ["./modules/i18n"], "@i18n/*": ["./modules/i18n/*"], "@shared/*": ["./modules/shared/*"], "content-collections": ["./.content-collections/generated"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.cjs", "**/*.mjs", ".next/types/**/*.ts", "../../config.ts"], "exclude": ["node_modules"]}