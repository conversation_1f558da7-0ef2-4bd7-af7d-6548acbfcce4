'use client'

import { useState, useCallback } from 'react'

export interface TransferImageData {
  file: File
  url: string
  name: string
  width: number
  height: number
  shouldShowProgress?: boolean // 是否应该显示进度条动画
}

export interface UploadConfig {
  maxFiles?: number
  maxFileSize?: number // in bytes
  acceptedTypes?: string[]
  onUploadStart?: () => void
  onUploadComplete?: (files: TransferImageData[]) => void
  onUploadError?: (error: string) => void
}

// Global state for image transfer between pages
let globalImageTransferData: TransferImageData[] = []
let globalImageTransferCallbacks: ((images: TransferImageData[]) => void)[] = []

/**
 * Hook for transferring images between pages
 * Used in HeroSection to prepare images and in ImageEditor to receive them
 */
export const useImageTransfer = () => {
  const [transferImages, setTransferImages] = useState<TransferImageData[]>([])

  // Set images to transfer (used in HeroSection)
  const setImagesToTransfer = useCallback((images: TransferImageData[]) => {
    globalImageTransferData = images
    setTransferImages(images)

    // Notify all listeners
    globalImageTransferCallbacks.forEach((callback) => callback(images))
  }, [])

  // Get and consume transferred images (used in ImageEditor)
  const getTransferredImages = useCallback((): TransferImageData[] => {
    const images = [...globalImageTransferData]
    globalImageTransferData = [] // Clear after consumption
    return images
  }, [])

  // Subscribe to image transfer updates
  const subscribeToImageTransfer = useCallback(
    (callback: (images: TransferImageData[]) => void) => {
      globalImageTransferCallbacks.push(callback)

      // Return unsubscribe function
      return () => {
        const index = globalImageTransferCallbacks.indexOf(callback)
        if (index > -1) {
          globalImageTransferCallbacks.splice(index, 1)
        }
      }
    },
    []
  )

  // Check if there are images waiting to be transferred
  const hasTransferredImages = useCallback((): boolean => {
    return globalImageTransferData.length > 0
  }, [])

  // Helper function to convert File to TransferImageData
  const fileToTransferData = useCallback(
    (file: File): Promise<TransferImageData> => {
      return new Promise((resolve, reject) => {
        const url = URL.createObjectURL(file)
        const img = new Image()

        img.onload = () => {
          resolve({
            file,
            url,
            name: file.name,
            width: img.naturalWidth,
            height: img.naturalHeight,
          })
        }

        img.onerror = () => {
          URL.revokeObjectURL(url)
          reject(new Error(`Failed to load image: ${file.name}`))
        }

        img.src = url
      })
    },
    []
  )

  // Helper function to convert multiple files
  const filesToTransferData = useCallback(
    async (files: File[]): Promise<TransferImageData[]> => {
      const promises = files.map((file) => fileToTransferData(file))
      return Promise.all(promises)
    },
    [fileToTransferData]
  )

  // Helper function to load image from URL (for preset images)
  const urlToTransferData = useCallback(
    (imageUrl: string, name?: string): Promise<TransferImageData> => {
      return new Promise((resolve, reject) => {
        // First fetch the image to convert to File
        fetch(imageUrl)
          .then((response) => response.blob())
          .then((blob) => {
            const file = new File([blob], name || 'preset-image.jpg', {
              type: blob.type,
            })
            return fileToTransferData(file)
          })
          .then(resolve)
          .catch(reject)
      })
    },
    [fileToTransferData]
  )

  // Generic upload handler
  const useImageUpload = useCallback(
    (config: UploadConfig = {}) => {
      const {
        maxFiles = 10,
        maxFileSize = 10 * 1024 * 1024, // 10MB
        acceptedTypes = ['image/jpeg', 'image/png', 'image/webp'],
        onUploadStart,
        onUploadComplete,
        onUploadError,
      } = config

      const [isDragActive, setIsDragActive] = useState(false)
      const [isUploading, setIsUploading] = useState(false)
      const [dragCounter, setDragCounter] = useState(0)

      const handleFiles = useCallback(
        async (files: File[]) => {
          if (files.length === 0) return

          // Validate file types
          const validFiles = files.filter(
            (file) =>
              file.type.startsWith('image/') &&
              acceptedTypes.includes(file.type)
          )

          if (validFiles.length === 0) {
            onUploadError?.(
              'Please select valid image files (JPG, PNG, or WebP)'
            )
            return
          }

          // Validate file sizes
          const validSizeFiles = validFiles.filter(
            (file) => file.size <= maxFileSize
          )
          if (validSizeFiles.length === 0) {
            onUploadError?.(
              `Files exceed ${Math.round(maxFileSize / (1024 * 1024))}MB limit`
            )
            return
          }

          // Limit number of files
          const limitedFiles = validSizeFiles.slice(0, maxFiles)

          setIsUploading(true)
          onUploadStart?.()

          try {
            const transferData = await filesToTransferData(limitedFiles)
            onUploadComplete?.(transferData)
          } catch (error) {
            console.error('Failed to process files:', error)
            onUploadError?.('Failed to process files. Please try again.')
          } finally {
            setIsUploading(false)
          }
        },
        [
          acceptedTypes,
          maxFileSize,
          maxFiles,
          onUploadStart,
          onUploadComplete,
          onUploadError,
          filesToTransferData,
        ]
      )

      const handleDragEnter = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        if (e.dataTransfer.types.includes('Files')) {
          setDragCounter((prev) => {
            const newCount = prev + 1
            if (newCount === 1) {
              setIsDragActive(true)
            }
            return newCount
          })
        }
      }, [])

      const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setDragCounter((prev) => {
          const newCount = prev - 1
          if (newCount === 0) {
            setIsDragActive(false)
          }
          return Math.max(0, newCount)
        })
      }, [])

      const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault()
        e.stopPropagation()
      }, [])

      const handleDrop = useCallback(
        (e: React.DragEvent) => {
          e.preventDefault()
          e.stopPropagation()
          setIsDragActive(false)
          setDragCounter(0)

          if (e.dataTransfer.files.length > 0) {
            const files = Array.from(e.dataTransfer.files)
            handleFiles(files)
          }
        },
        [handleFiles]
      )

      const handleFileInputChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
          if (e.target.files && e.target.files.length > 0) {
            const files = Array.from(e.target.files)
            handleFiles(files)
          }
        },
        [handleFiles]
      )

      return {
        isDragActive,
        isUploading,
        handleFiles,
        handleDragEnter,
        handleDragLeave,
        handleDragOver,
        handleDrop,
        handleFileInputChange,
      }
    },
    [filesToTransferData]
  )

  return {
    transferImages,
    setImagesToTransfer,
    getTransferredImages,
    subscribeToImageTransfer,
    hasTransferredImages,
    fileToTransferData,
    filesToTransferData,
    urlToTransferData,
    useImageUpload,
  }
}
