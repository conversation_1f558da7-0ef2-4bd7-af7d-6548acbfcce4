{"dependencies": {"@prisma/client": "^5.22.0", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/node": "22.9.0", "dotenv-cli": "^7.4.2", "prisma": "^5.22.0", "tsconfig": "workspace:*", "zod-prisma-types": "^3.1.8"}, "main": "./index.tsx", "name": "database", "scripts": {"db:generate": "prisma generate", "db:push": "dotenv -c -e ../../.env -- prisma db push --skip-generate", "db:studio": "dotenv -c -e ../../.env -- prisma studio", "type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}