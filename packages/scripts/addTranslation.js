// scripts/addTranslation.js
const fs = require('fs')
const path = require('path')
const translations = {
  de: {
    myPPT: 'Meine PPT',
  },
  en: {
    myPPT: 'My PPT',
  },
  fr: {
    myPPT: 'Mon PPT',
  },
  ja: {
    myPPT: '私のPPT',
  },
  ko: {
    myPPT: '내 PPT',
  },
  pt: {
    myPPT: 'Meu PPT',
  },
  'zh-CN': {
    myPPT: '我的PPT',
  },
}
const LOCALES_DIR = path.join(__dirname, '../i18n/translations')

function addTranslation() {
  // 确保目录存在
  if (!fs.existsSync(LOCALES_DIR)) {
    console.error(`❌ Directory not found: ${LOCALES_DIR}`)
    return
  }

  fs.readdirSync(LOCALES_DIR).forEach((file) => {
    if (file.endsWith('.json')) {
      const lang = file.replace('.json', '')
      const filePath = path.join(LOCALES_DIR, file)

      if (!translations[lang]) {
        console.log(`⚠️ No translations found for ${lang}, skipping...`)
        return
      }

      try {
        // 读取现有文件内容
        let existingTranslations = {}
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, 'utf8')
          existingTranslations = JSON.parse(content)
        }

        // 合并翻译，新翻译会覆盖旧翻译
        const updatedTranslations = {
          ...existingTranslations,
          ...translations[lang],
        }

        // 写入文件，保持格式化
        fs.writeFileSync(
          filePath,
          JSON.stringify(updatedTranslations, null, 2) + '\n',
          'utf8'
        )

        console.log(`✅ Successfully updated ${file}`)
      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error.message)
      }
    }
  })
}

addTranslation()
