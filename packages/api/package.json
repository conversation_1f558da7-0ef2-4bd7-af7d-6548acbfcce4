{"dependencies": {"@lemonsqueezy/lemonsqueezy.js": "^4.0.0", "@trpc/server": "11.0.0-rc.638", "auth": "workspace:*", "change-case": "^5.4.4", "chargebee-typescript": "^2.42.0", "database": "workspace:*", "logs": "workspace:*", "mail": "workspace:*", "next": "15.0.3", "openai": "^4.72.0", "storage": "workspace:*", "stripe": "^17.3.1", "superjson": "^2.2.1", "use-intl": "^3.25.1", "utils": "workspace:*", "zod": "^3.23.8"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/react": "18.3.12", "encoding": "^0.1.13", "prisma": "^5.22.0", "tsconfig": "workspace:*", "typescript": "5.6.3"}, "main": "./index.tsx", "name": "api", "scripts": {"type-check": "tsc --noEmit"}, "types": "./**/.tsx", "version": "0.0.0"}