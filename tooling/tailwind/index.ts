import variablesPlugin from '@mertasan/tailwindcss-variables'
import colorVariable from '@mertasan/tailwindcss-variables/colorVariable'
import containerQueryPlugin from '@tailwindcss/container-queries'
import formsPlugin from '@tailwindcss/forms'
import typographyPlugin from '@tailwindcss/typography'
import type { Config } from 'tailwindcss'
import animatePlugin from 'tailwindcss-animate'

// 吉卜力风格浅色模式变量
export const lightVariables = {
  colors: {
    border: '#E0E7EB', // 淡灰蓝边框
    input: '#F0F5F9', // 云朵色
    ring: '#219EBC', // 湖水蓝
    background: '#F6F1E6', // 纸张质感色
    foreground: '#2B3A42', // 深墨色
    primary: '#219EBC', // 湖水蓝
    'primary-foreground': '#FFFFFF', // 白色
    secondary: '#FB8500', // 温暖橙色
    'secondary-foreground': '#FFFFFF', // 白色
    destructive: '#E74C3C', // 错误红
    'destructive-foreground': '#FFFFFF', // 白色
    success: '#76C893', // 草地绿
    'success-foreground': '#FFFFFF', // 白色
    muted: '#FFF9E8', // 奶油色
    'muted-foreground': '#6C7A89', // 淡灰色
    accent: '#76C893', // 草地绿
    'accent-foreground': '#2B3A42', // 深墨色
    popover: '#FFFFFF', // 白色
    'popover-foreground': '#2B3A42', // 深墨色
    card: '#FFFFFF', // 白色
    'card-foreground': '#2B3A42', // 深墨色
    highlight: '#FFB703', // 金色阳光
    'highlight-foreground': '#2B3A42', // 深墨色
  },
}

// 吉卜力风格暗色模式变量
export const darkVariables = {
  colors: {
    border: '#3F4E5C', // 中性灰色
    input: '#2B3A42', // 深墨色
    ring: '#8ECAE6', // 天空蓝浅色
    background: '#1A2530', // 深夜蓝色
    foreground: '#F0F5F9', // 云朵色
    primary: '#8ECAE6', // 天空蓝浅色
    'primary-foreground': '#2B3A42', // 深墨色
    secondary: '#FFB703', // 金色阳光
    'secondary-foreground': '#2B3A42', // 深墨色
    destructive: '#E74C3C', // 错误红
    'destructive-foreground': '#FFFFFF', // 白色
    success: '#B5E48C', // 嫩芽绿
    'success-foreground': '#2B3A42', // 深墨色
    muted: '#253342', // 深蓝灰色
    'muted-foreground': '#A3B4C4', // 浅蓝灰色
    accent: '#B5E48C', // 嫩芽绿
    'accent-foreground': '#2B3A42', // 深墨色
    popover: '#1A2530', // 深夜蓝色
    'popover-foreground': '#F0F5F9', // 云朵色
    card: '#253342', // 深蓝灰色
    'card-foreground': '#F0F5F9', // 云朵色
    highlight: '#FB8500', // 温暖橙色
    'highlight-foreground': '#2B3A42', // 深墨色
  },
}

export default {
  content: [],
  darkMode: ['class'],
  theme: {
    container: {
      center: true,
      padding: '2rem',
      screens: {
        '2xl': '1280px',
      },
    },
    extend: {
      boxShadow: {
        sm: '0 2px 8px 0 rgb(118, 200, 147, 0.05), 0 0 1px rgba(0,0,0,0.05)',
        DEFAULT:
          '0 4px 16px 0 rgb(118, 200, 147, 0.08), 0 0 1px rgba(0,0,0,0.05)',
        md: '0 6px 24px 0 rgb(118, 200, 147, 0.1), 0 0 1px rgba(0,0,0,0.05)',
        lg: '0 8px 32px 0 rgb(118, 200, 147, 0.12), 0 0 1px rgba(0,0,0,0.05)',
        xl: '0 12px 48px 0 rgb(118, 200, 147, 0.15), 0 0 1px rgba(0,0,0,0.05)',
        '2xl':
          '0 16px 64px 0 rgb(118, 200, 147, 0.18), 0 0 1px rgba(0,0,0,0.05)',
        totoro: '0 4px 16px rgba(118, 200, 147, 0.3)', // 龙猫主题阴影
        spirited: '0 4px 16px rgba(33, 158, 188, 0.3)', // 千与千寻主题阴影
        howl: '0 4px 16px rgba(251, 133, 0, 0.3)', // 哈尔主题阴影
      },
      borderRadius: {
        lg: '0.75rem',
        md: 'calc(0.75rem - 2px)',
        sm: 'calc(0.75rem - 4px)',
        ghibli: '1rem', // 更圆润的边角
      },
      fontFamily: {
        sans: ['var(--font-sans)', 'sans-serif'],
        ghibli: ['var(--font-sans)', 'Quicksand', 'sans-serif'],
      },
      keyframes: {
        'accordion-down': {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        'accordion-up': {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        sway: {
          '0%, 100%': { transform: 'rotate(-3deg)' },
          '50%': { transform: 'rotate(3deg)' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        float: 'float 6s ease-in-out infinite',
        sway: 'sway 4s ease-in-out infinite',
      },
      backgroundImage: {
        clouds:
          'url(\'data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000" preserveAspectRatio="none"%3E%3Cpath fill="%23F0F5F9" d="M0,0 C200,100 300,50 500,150 C700,250 800,200 1000,300 L1000,1000 L0,1000 Z"%3E%3C/path%3E%3C/svg%3E\')',
        'gradient-totoro': 'linear-gradient(to right, #76C893, #B5E48C)',
        'gradient-spirited': 'linear-gradient(to right, #219EBC, #8ECAE6)',
        'gradient-howl': 'linear-gradient(to right, #FB8500, #FFB703)',
      },
      colors: {
        border: colorVariable('--colors-border'),
        input: colorVariable('--colors-input'),
        ring: colorVariable('--colors-ring'),
        background: colorVariable('--colors-background'),
        foreground: colorVariable('--colors-foreground'),
        primary: {
          DEFAULT: colorVariable('--colors-primary'),
          foreground: colorVariable('--colors-primary-foreground'),
        },
        secondary: {
          DEFAULT: colorVariable('--colors-secondary'),
          foreground: colorVariable('--colors-secondary-foreground'),
        },
        destructive: {
          DEFAULT: colorVariable('--colors-destructive'),
          foreground: colorVariable('--colors-destructive-foreground'),
        },
        success: {
          DEFAULT: colorVariable('--colors-success'),
          foreground: colorVariable('--colors-success-foreground'),
        },
        muted: {
          DEFAULT: colorVariable('--colors-muted'),
          foreground: colorVariable('--colors-muted-foreground'),
        },
        accent: {
          DEFAULT: colorVariable('--colors-accent'),
          foreground: colorVariable('--colors-accent-foreground'),
        },
        popover: {
          DEFAULT: colorVariable('--colors-popover'),
          foreground: colorVariable('--colors-popover-foreground'),
        },
        card: {
          DEFAULT: colorVariable('--colors-card'),
          foreground: colorVariable('--colors-card-foreground'),
        },
        highlight: {
          DEFAULT: colorVariable('--colors-highlight'),
          foreground: colorVariable('--colors-highlight-foreground'),
        },
      },
    },
    variables: {
      DEFAULT: lightVariables,
    },
    darkVariables: {
      DEFAULT: darkVariables,
    },
  },
  plugins: [
    formsPlugin({
      strategy: 'base',
    }),
    typographyPlugin,
    animatePlugin,
    containerQueryPlugin,
    variablesPlugin({
      colorVariables: true,
    }),
  ],
} satisfies Config
